#!/usr/bin/env python3
"""
可视化函数使用示例
展示如何使用不同的参数控制可视化效果
"""

from explain import quick_viz, simple_high_impact_viz

def example_usage():
    """
    展示不同参数设置的使用示例
    """
    
    # 假设已经有了 subgraph, edge_mask, node_feat_mask, center_node 等变量
    
    print("=== 可视化参数使用示例 ===")
    
    # 示例1: 简洁可视化 - 适合快速查看
    print("\n1. 简洁可视化 (适合快速查看)")
    """
    quick_viz(
        graph=subgraph,
        edge_mask=edge_mask,
        node_mask=node_feat_mask,
        node_idx=center_node,
        original_target_id=1063,
        save_path="simple_view.png",
        max_nodes=8,   # 只显示8个最重要的节点
        max_edges=5    # 只显示5条最重要的边
    )
    """
    
    # 示例2: 中等详细度可视化 - 平衡信息量和清晰度
    print("\n2. 中等详细度可视化 (平衡信息量和清晰度)")
    """
    quick_viz(
        graph=subgraph,
        edge_mask=edge_mask,
        node_mask=node_feat_mask,
        node_idx=center_node,
        original_target_id=1063,
        save_path="medium_view.png",
        max_nodes=15,  # 显示15个节点
        max_edges=10   # 显示10条边
    )
    """
    
    # 示例3: 详细可视化 - 显示更多信息
    print("\n3. 详细可视化 (显示更多信息)")
    """
    simple_high_impact_viz(
        graph=subgraph,
        edge_mask=edge_mask,
        node_mask=node_feat_mask,
        node_idx=center_node,
        original_target_id=1063,
        save_path="detailed_view.png",
        top_edges=20,     # 连通子图中选择20条边
        top_nodes=25,     # 考虑25个重要节点
        max_nodes=30,     # 最多显示30个节点
        max_edges=25      # 最多显示25条边
    )
    """
    
    # 示例4: 超详细可视化 - 用于深入分析
    print("\n4. 超详细可视化 (用于深入分析)")
    """
    simple_high_impact_viz(
        graph=subgraph,
        edge_mask=edge_mask,
        node_mask=node_feat_mask,
        node_idx=center_node,
        original_target_id=1063,
        save_path="ultra_detailed_view.png",
        top_edges=50,     # 连通子图中选择50条边
        top_nodes=40,     # 考虑40个重要节点
        max_nodes=50,     # 最多显示50个节点
        max_edges=40      # 最多显示40条边
    )
    """

def parameter_guide():
    """
    参数选择指南
    """
    print("\n=== 参数选择指南 ===")
    
    print("\n📊 根据分析目的选择参数:")
    print("1. 快速概览: max_nodes=8, max_edges=5")
    print("2. 一般分析: max_nodes=15, max_edges=10") 
    print("3. 详细分析: max_nodes=25, max_edges=20")
    print("4. 深入研究: max_nodes=40+, max_edges=30+")
    
    print("\n🎯 参数说明:")
    print("- top_edges: 连通子图构建时考虑的边数")
    print("- top_nodes: 从节点特征重要性中选择的节点数")
    print("- max_edges: 最终可视化显示的最大边数")
    print("- max_nodes: 最终可视化显示的最大节点数")
    
    print("\n⚖️ 平衡原则:")
    print("- 节点太少: 可能丢失重要信息")
    print("- 节点太多: 图形过于复杂，难以理解")
    print("- 边太少: 连通性信息不足")
    print("- 边太多: 视觉混乱")
    
    print("\n🔗 连通性保证:")
    print("- 所有显示的节点都与目标节点连通")
    print("- 优先显示与目标节点直接相连的边")
    print("- 通过BFS确保子图的连通性")

if __name__ == '__main__':
    example_usage()
    parameter_guide()
