import json
import warnings
import random
from functools import partial
from itertools import chain

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import dgl
from dgl.nn.pytorch.explain import GNNExplainer
from dgl.ops import edge_softmax
import dgl.function as fn
from dgl.utils import expand_as_pair

from utils.loaddata import load_metadata, load_batch_level_dataset, load_entity_level_dataset
from utils.poolers import Pooling
from utils.config import build_args
from utils.utils import create_norm, create_activation

import matplotlib
matplotlib.use('Agg')  # 设置非交互式后端，避免tkinter线程错误
import matplotlib.pyplot as plt
import networkx as nx
from matplotlib.colors import LinearSegmentedColormap

warnings.filterwarnings('ignore')

with open(f'./data/theia/train_node_map.json', 'r') as f:
    train_node_map = json.load(f)
with open(f'./data/theia/test_node_map.json', 'r') as f:
    test_node_map = json.load(f)
a = 1

def sce_loss(x, y, alpha=3):
    x = F.normalize(x, p=2, dim=-1)
    y = F.normalize(y, p=2, dim=-1)
    loss = (1 - (x * y).sum(dim=-1)).pow_(alpha)
    loss = loss.mean()
    return loss

class GATConv(nn.Module):
    def __init__(self,
                 in_dim,
                 e_dim,
                 out_dim,
                 n_heads,
                 feat_drop=0.0,
                 attn_drop=0.0,
                 negative_slope=0.2,
                 residual=False,
                 activation=None,
                 allow_zero_in_degree=False,
                 bias=True,
                 norm=None,
                 concat_out=True,
                 generator=False):
        super(GATConv, self).__init__()
        self.n_heads = n_heads
        self.src_feat, self.dst_feat = expand_as_pair(in_dim)
        self.edge_feat = e_dim
        self.out_feat = out_dim
        self.allow_zero_in_degree = allow_zero_in_degree
        self.concat_out = concat_out

        if isinstance(in_dim, tuple):
            self.fc_node_embedding = nn.Linear(
                self.src_feat, self.out_feat * self.n_heads, bias=False)
            self.fc_src = nn.Linear(self.src_feat, self.out_feat * self.n_heads, bias=False)
            self.fc_dst = nn.Linear(self.dst_feat, self.out_feat * self.n_heads, bias=False)
        else:
            self.fc_node_embedding = nn.Linear(
                self.src_feat, self.out_feat * self.n_heads, bias=False)
            self.fc = nn.Linear(self.src_feat, self.out_feat * self.n_heads, bias=False)
        self.edge_fc = nn.Linear(self.edge_feat, self.out_feat * self.n_heads, bias=False)
        self.attn_h = nn.Parameter(torch.FloatTensor(size=(1, self.n_heads, self.out_feat)))
        self.attn_e = nn.Parameter(torch.FloatTensor(size=(1, self.n_heads, self.out_feat)))
        self.attn_t = nn.Parameter(torch.FloatTensor(size=(1, self.n_heads, self.out_feat)))
        self.feat_drop = nn.Dropout(feat_drop)
        self.attn_drop = nn.Dropout(attn_drop)
        self.leaky_relu = nn.LeakyReLU(negative_slope)
        if bias:
            self.bias = nn.Parameter(torch.FloatTensor(size=(1, self.n_heads, self.out_feat)))
        else:
            self.register_buffer('bias', None)
        if residual:
            if self.dst_feat != self.n_heads * self.out_feat:
                self.res_fc = nn.Linear(
                    self.dst_feat, self.n_heads * self.out_feat, bias=False)
            else:
                self.res_fc = nn.Identity()
        else:
            self.register_buffer('res_fc', None)
        self.reset_parameters()
        self.activation = activation
        self.norm = norm
        if norm is not None:
            self.norm = norm(self.n_heads * self.out_feat)

        self.generator=generator     # 加的，触发器生成器时不管边权重
    def reset_parameters(self):
        gain = nn.init.calculate_gain('relu')
        nn.init.xavier_normal_(self.edge_fc.weight, gain=gain)
        if hasattr(self, 'fc'):
            nn.init.xavier_normal_(self.fc.weight, gain=gain)
        else:
            nn.init.xavier_normal_(self.fc_src.weight, gain=gain)
            nn.init.xavier_normal_(self.fc_dst.weight, gain=gain)
        nn.init.xavier_normal_(self.attn_h, gain=gain)
        nn.init.xavier_normal_(self.attn_e, gain=gain)
        nn.init.xavier_normal_(self.attn_t, gain=gain)
        if self.bias is not None:
            nn.init.constant_(self.bias, 0)
        if isinstance(self.res_fc, nn.Linear):
            nn.init.xavier_normal_(self.res_fc.weight, gain=gain)

    def set_allow_zero_in_degree(self, set_value):
        self.allow_zero_in_degree = set_value

    def forward(self, graph, feat, get_attention=False):
        # ============添加边权重==============
        if 'edge_weights' in graph.edata:
            edge_weights = graph.edata['edge_weights']
        else:
            edge_weights = None
        # ===========================
            
        edge_feature = graph.edata['attr']
        with graph.local_scope():
            if isinstance(feat, tuple):
                src_prefix_shape = feat[0].shape[:-1]
                dst_prefix_shape = feat[1].shape[:-1]
                h_src = self.feat_drop(feat[0])
                h_dst = self.feat_drop(feat[1])
                if not hasattr(self, 'fc_src'):
                    feat_src = self.fc(h_src).view(
                        *src_prefix_shape, self.n_heads, self.out_feat)
                    feat_dst = self.fc(h_dst).view(
                        *dst_prefix_shape, self.n_heads, self.out_feat)
                else:
                    feat_src = self.fc_src(h_src).view(
                        *src_prefix_shape, self.n_heads, self.out_feat)
                    feat_dst = self.fc_dst(h_dst).view(
                        *dst_prefix_shape, self.n_heads, self.out_feat)
            else:
                src_prefix_shape = dst_prefix_shape = feat.shape[:-1]
                h_src = h_dst = self.feat_drop(feat)
                feat_src = feat_dst = self.fc(h_src).view(
                    *src_prefix_shape, self.n_heads, self.out_feat)
                if graph.is_block:
                    feat_dst = feat_src[:graph.number_of_dst_nodes()]
                    h_dst = h_dst[:graph.number_of_dst_nodes()]
                    dst_prefix_shape = (graph.number_of_dst_nodes(),) + dst_prefix_shape[1:]
            edge_prefix_shape = edge_feature.shape[:-1]
            eh = (feat_src * self.attn_h).sum(-1).unsqueeze(-1)
            et = (feat_dst * self.attn_t).sum(-1).unsqueeze(-1)

            graph.srcdata.update({'hs': feat_src, 'eh': eh})
            graph.dstdata.update({'et': et})

            feat_edge = self.edge_fc(edge_feature).view(
                *edge_prefix_shape, self.n_heads, self.out_feat)
            ee = (feat_edge * self.attn_e).sum(-1).unsqueeze(-1)

            graph.edata.update({'ee': ee})
            graph.apply_edges(fn.u_add_e('eh', 'ee', 'ee'))
            graph.apply_edges(fn.e_add_v('ee', 'et', 'e'))
            e = self.leaky_relu(graph.edata.pop('e'))
            
            if not self.generator:  # 归一化前mask，并乘以权重
                if edge_weights is not None:  # 同时处理乘以权重和无边mask操作
                    hard_mask = (edge_weights > 0.5).float()
                    edge_weights = edge_weights + (hard_mask - edge_weights).detach()  # 前向传播 —— 硬化   反向传播 —— 连续
                    for _ in range(e.dim() - edge_weights.dim()):
                        edge_weights = edge_weights.unsqueeze(-1)
                    e = e * edge_weights + (-1e9) * (1 - edge_weights)  # 近似 -inf
                attn_scores = edge_softmax(graph, e)

                graph.edata['a'] = self.attn_drop(attn_scores)

            else:
                graph.edata['a'] = self.attn_drop(edge_softmax(graph, e))
                
            # Message passing
            graph.update_all(fn.u_mul_e('hs', 'a', 'm'),
                            fn.sum('m', 'hs'))

            rst = graph.dstdata['hs'].view(-1, self.n_heads, self.out_feat)

            if self.bias is not None:
                rst = rst + self.bias.view(
                    *((1,) * len(dst_prefix_shape)), self.n_heads, self.out_feat)

            if self.res_fc is not None:
                resval = self.res_fc(h_dst).view(*dst_prefix_shape, -1, self.out_feat)
                rst = rst + resval

            if self.concat_out:
                rst = rst.flatten(1)
            else:
                rst = torch.mean(rst, dim=1)

            if self.norm is not None:
                rst = self.norm(rst)

            if self.activation:
                rst = self.activation(rst)

            if get_attention:
                return rst, graph.edata['a']
            else:
                return rst

class GAT(nn.Module):
    def __init__(self,
                 n_dim,
                 e_dim,
                 hidden_dim,
                 out_dim,
                 n_layers,
                 n_heads,
                 n_heads_out,
                 activation,
                 feat_drop,
                 attn_drop,
                 negative_slope,
                 residual,
                 norm,
                 concat_out=False,
                 encoding=False
                 ):
        super(GAT, self).__init__()
        self.out_dim = out_dim
        self.n_heads = n_heads
        self.n_layers = n_layers
        self.gats = nn.ModuleList()
        self.concat_out = concat_out

        last_activation = create_activation(activation) if encoding else None
        last_residual = (encoding and residual)
        last_norm = norm if encoding else None

        if self.n_layers == 1:
            self.gats.append(GATConv(
                n_dim, e_dim, out_dim, n_heads_out, feat_drop, attn_drop, negative_slope,
                last_residual, norm=last_norm, concat_out=self.concat_out
            ))
        else:
            self.gats.append(GATConv(
                n_dim, e_dim, hidden_dim, n_heads, feat_drop, attn_drop, negative_slope,
                residual, create_activation(activation),
                norm=norm, concat_out=self.concat_out
            ))
            for _ in range(1, self.n_layers - 1):
                self.gats.append(GATConv(
                    hidden_dim * self.n_heads, e_dim, hidden_dim, n_heads,
                    feat_drop, attn_drop, negative_slope,
                    residual, create_activation(activation),
                    norm=norm, concat_out=self.concat_out
                ))
            self.gats.append(GATConv(
                hidden_dim * self.n_heads, e_dim, out_dim, n_heads_out,
                feat_drop, attn_drop, negative_slope,
                last_residual, last_activation, norm=last_norm, concat_out=self.concat_out
            ))
        self.head = nn.Identity()

    def forward(self, g, input_feature, return_hidden=False):
        h = input_feature
        hidden_list = []
        for layer in range(self.n_layers):
            h = self.gats[layer](g, h)
            hidden_list.append(h)
        if return_hidden:
            return self.head(h), hidden_list
        else:
            return self.head(h)

    def reset_classifier(self, num_classes):
        self.head = nn.Linear(self.num_heads * self.out_dim, num_classes)

def build_model(args):
    num_hidden = args.num_hidden
    num_layers = args.num_layers
    negative_slope = args.negative_slope
    mask_rate = args.mask_rate
    alpha_l = args.alpha_l
    n_dim = args.n_dim
    e_dim = args.e_dim

    model = GMAEModel(
        n_dim=n_dim,
        e_dim=e_dim,
        hidden_dim=num_hidden,
        n_layers=num_layers,
        n_heads=4,
        activation="prelu",
        feat_drop=0.1,
        negative_slope=negative_slope,
        residual=True,
        mask_rate=mask_rate,
        norm='BatchNorm',
        loss_fn='sce',
        alpha_l=alpha_l
    )
    return model


class GMAEModel(nn.Module):
    def __init__(self, n_dim, e_dim, hidden_dim, n_layers, n_heads, activation,
                 feat_drop, negative_slope, residual, norm, mask_rate=0.5, loss_fn="sce", alpha_l=2):
        super(GMAEModel, self).__init__()
        self._mask_rate = mask_rate
        self._output_hidden_size = hidden_dim
        self.recon_loss = nn.BCELoss(reduction='mean')

        def init_weights(m):
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform(m.weight)
                nn.init.constant_(m.bias, 0)

        self.edge_recon_fc = nn.Sequential(
            nn.Linear(hidden_dim * n_layers * 2, hidden_dim),
            nn.LeakyReLU(negative_slope),
            nn.Linear(hidden_dim, 1),
            nn.Sigmoid()
        )
        self.edge_recon_fc.apply(init_weights)

        assert hidden_dim % n_heads == 0
        enc_num_hidden = hidden_dim // n_heads
        enc_nhead = n_heads

        dec_in_dim = hidden_dim
        dec_num_hidden = hidden_dim

        # build encoder
        self.encoder = GAT(
            n_dim=n_dim,
            e_dim=e_dim,
            hidden_dim=enc_num_hidden,
            out_dim=enc_num_hidden,
            n_layers=n_layers,
            n_heads=enc_nhead,
            n_heads_out=enc_nhead,
            concat_out=True,
            activation=activation,
            feat_drop=feat_drop,
            attn_drop=0.0,
            negative_slope=negative_slope,
            residual=residual,
            norm=create_norm(norm),
            encoding=True,
        )

        # build decoder for attribute prediction
        self.decoder = GAT(
            n_dim=dec_in_dim,
            e_dim=e_dim,
            hidden_dim=dec_num_hidden,
            out_dim=n_dim,
            n_layers=1,
            n_heads=n_heads,
            n_heads_out=1,
            concat_out=True,
            activation=activation,
            feat_drop=feat_drop,
            attn_drop=0.0,
            negative_slope=negative_slope,
            residual=residual,
            norm=create_norm(norm),
            encoding=False,
        )

        self.enc_mask_token = nn.Parameter(torch.zeros(1, n_dim))
        self.encoder_to_decoder = nn.Linear(dec_in_dim * n_layers, dec_in_dim, bias=False)

        # * setup loss function
        self.criterion = self.setup_loss_fn(loss_fn, alpha_l)

    @property
    def output_hidden_dim(self):
        return self._output_hidden_size

    def setup_loss_fn(self, loss_fn, alpha_l):
        if loss_fn == "sce":
            criterion = partial(sce_loss, alpha=alpha_l)
        else:
            raise NotImplementedError
        return criterion

    def encoding_mask_noise(self, g, mask_rate=0.3):
        new_g = g.clone()
        num_nodes = g.num_nodes()
        perm = torch.randperm(num_nodes, device=g.device)

        # random masking
        num_mask_nodes = int(mask_rate * num_nodes)
        mask_nodes = perm[: num_mask_nodes]
        keep_nodes = perm[num_mask_nodes:]

        new_g.ndata["attr"][mask_nodes] = self.enc_mask_token

        return new_g, (mask_nodes, keep_nodes)

    def forward(self, g):
        loss = self.compute_loss(g)
        return loss

    def compute_loss(self, g):
        # Feature Reconstruction
        pre_use_g, (mask_nodes, keep_nodes) = self.encoding_mask_noise(g, self._mask_rate)
        pre_use_x = pre_use_g.ndata['attr'].to(pre_use_g.device)
        use_g = pre_use_g
        enc_rep, all_hidden = self.encoder(use_g, pre_use_x, return_hidden=True)
        return_enc_rep = enc_rep.clone()
        enc_rep = torch.cat(all_hidden, dim=1)
        rep = self.encoder_to_decoder(enc_rep)

        recon = self.decoder(pre_use_g, rep)
        x_init = g.ndata['attr'][mask_nodes]
        x_rec = recon[mask_nodes]
        loss = self.criterion(x_rec, x_init)

        # Structural Reconstruction
        threshold = min(10000, g.num_nodes())

        negative_edge_pairs = dgl.sampling.global_uniform_negative_sampling(g, threshold)
        positive_edge_pairs = random.sample(range(g.number_of_edges()), threshold)
        positive_edge_pairs = (g.edges()[0][positive_edge_pairs], g.edges()[1][positive_edge_pairs])
        sample_src = enc_rep[torch.cat([positive_edge_pairs[0], negative_edge_pairs[0]])].to(g.device)
        sample_dst = enc_rep[torch.cat([positive_edge_pairs[1], negative_edge_pairs[1]])].to(g.device)
        y_pred = self.edge_recon_fc(torch.cat([sample_src, sample_dst], dim=-1)).squeeze(-1)
        y = torch.cat([torch.ones(len(positive_edge_pairs[0])), torch.zeros(len(negative_edge_pairs[0]))]).to(
            g.device)
        loss += self.recon_loss(y_pred, y)
        return loss, return_enc_rep

    def embed(self, g):
        x = g.ndata['attr'].to(g.device)
        rep = self.encoder(g, x)
        return rep

    @property
    def enc_params(self):
        return self.encoder.parameters()

    @property
    def dec_params(self):
        return chain(*[self.encoder_to_decoder.parameters(), self.decoder.parameters()])

class GATExplainerWrapper(nn.Module):
    """包装GAT模型以适配GNNExplainer的接口要求"""
    def __init__(self, model):
        super().__init__()
        self.model = model
        
    def forward(self, graph, feat, eweight=None):
        """
        包装forward函数以符合GNNExplainer的要求
        
        Parameters
        ----------
        graph : DGLGraph
            输入图
        feat : Tensor
            节点特征
        eweight : Tensor, optional
            边权重，如果提供则用于消息传递
        """
        # 将模型设置为评估模式
        self.model.eval()
        
        # 如果提供了边权重，将其添加到图的边数据中
        if eweight is not None:
            graph.edata['edge_weights'] = eweight
            
        # 获取编码器的输出
        z = self.model.embed(graph)
        
        return z

def visualize_explanation(graph, edge_mask, node_mask=None, node_idx=None,
                         edge_threshold=None, node_threshold=None,
                         top_edge_percent=20, top_node_percent=30,
                         save_path=None, layout='spring',
                         figsize=(10, 5), dpi=300, font_size=5):
    """
    智能过滤版解释可视化 - 只显示高影响力的节点和边

    Parameters
    ----------
    graph : DGLGraph
        输入图
    edge_mask : Tensor
        边重要性掩码 (值范围0-1)
    node_mask : Tensor, optional
        节点重要性掩码 (值范围0-1)
    node_idx : int, optional
        需要高亮的中心节点索引
    edge_threshold : float, optional
        边显示阈值，如果为None则使用top_edge_percent
    node_threshold : float, optional
        节点显示阈值，如果为None则使用top_node_percent
    top_edge_percent : float, optional
        显示前百分之多少的重要边 (默认20%)
    top_node_percent : float, optional
        显示前百分之多少的重要节点 (默认30%)
    save_path : str, optional
        图片保存路径
    layout : str, optional
        布局算法 ('spring'|'circular'|'kamada')
    figsize : tuple, optional
        图形大小
    dpi : int, optional
        图片分辨率
    font_size : int, optional
        字体大小
    """
    import networkx as nx
    import matplotlib.pyplot as plt
    import numpy as np
    from matplotlib.colors import LinearSegmentedColormap

    # 创建自定义颜色映射
    node_cmap = LinearSegmentedColormap.from_list('custom_blue',
                                                 ['#E6F3FF', '#0066CC'])
    edge_cmap = LinearSegmentedColormap.from_list('custom_red',
                                                 ['#FFE6E6', '#CC0000'])

    # 转换为networkx图并处理掩码
    nx_graph = graph.to_networkx()
    edge_weights = edge_mask.detach().cpu().numpy()

    # 智能边过滤：使用阈值或百分比
    if edge_threshold is not None:
        # 使用固定阈值
        edges_to_keep = edge_weights >= edge_threshold
        print(f"使用边阈值 {edge_threshold}，保留 {edges_to_keep.sum()}/{len(edge_weights)} 条边")
    else:
        # 使用百分比过滤，只保留前top_edge_percent%的重要边
        edge_percentile = np.percentile(edge_weights, 100 - top_edge_percent)
        edges_to_keep = edge_weights >= edge_percentile
        print(f"保留前 {top_edge_percent}% 重要边：{edges_to_keep.sum()}/{len(edge_weights)} 条边 (阈值: {edge_percentile:.4f})")

    filtered_edges = [(u, v) for (u, v), keep in zip(nx_graph.edges(), edges_to_keep) if keep]
    filtered_weights = edge_weights[edges_to_keep]
    
    # 获取有边连接的节点
    nodes_with_edges = set()
    for u, v in filtered_edges:
        nodes_with_edges.add(u)
        nodes_with_edges.add(v)

    # 智能节点过滤：如果有节点掩码，进一步过滤节点
    if node_mask is not None:
        node_weights = node_mask.detach().cpu().numpy()

        if node_threshold is not None:
            # 使用固定阈值
            important_nodes = set(np.where(node_weights >= node_threshold)[0])
            print(f"使用节点阈值 {node_threshold}，保留 {len(important_nodes)}/{len(node_weights)} 个节点")
        else:
            # 使用百分比过滤，只保留前top_node_percent%的重要节点
            node_percentile = np.percentile(node_weights, 100 - top_node_percent)
            important_nodes = set(np.where(node_weights >= node_percentile)[0])
            print(f"保留前 {top_node_percent}% 重要节点：{len(important_nodes)}/{len(node_weights)} 个节点 (阈值: {node_percentile:.4f})")

        # 确保中心节点始终被包含
        if node_idx is not None:
            important_nodes.add(node_idx)

        # 只保留既有边连接又重要的节点
        final_nodes = nodes_with_edges.intersection(important_nodes)

        # 重新过滤边，只保留连接重要节点的边
        final_edges = [(u, v) for u, v in filtered_edges if u in final_nodes and v in final_nodes]
        final_weights = []
        for i, (u, v) in enumerate(filtered_edges):
            if u in final_nodes and v in final_nodes:
                final_weights.append(filtered_weights[i])
        final_weights = np.array(final_weights)

        print(f"最终保留 {len(final_nodes)} 个节点和 {len(final_edges)} 条边")
    else:
        # 没有节点掩码时，使用所有有边连接的节点
        final_nodes = nodes_with_edges
        final_edges = filtered_edges
        final_weights = filtered_weights

    # 创建最终子图
    subgraph = nx.Graph()
    subgraph.add_nodes_from(final_nodes)
    subgraph.add_edges_from(final_edges)
    
    # 创建图形和子图
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=figsize, dpi=dpi)
    plt.rcParams['font.size'] = font_size
    
    # 获取节点类型
    node_types = graph.ndata['type'].detach().cpu().numpy()
    
    # 布局选择
    layout_funcs = {
        'spring': nx.spring_layout,
        'circular': nx.circular_layout,
        'kamada': nx.kamada_kawai_layout
    }
    pos = layout_funcs[layout](subgraph, k=1.5)  # 增加k值使节点分布更分散
    
    # 绘制两个子图的函数
    def draw_subgraph(ax, show_node_labels=True):
        # 节点颜色设置
        if node_mask is not None:
            node_mask_np = node_mask.detach().cpu().numpy()
            node_importance = np.mean(node_mask_np)
            node_colors = np.ones(len(subgraph.nodes())) * node_importance
            cmap = node_cmap
            vmin, vmax = 0, 1
            node_sizes = np.ones(len(subgraph.nodes())) * 100
            node_sizes += node_importance * 400
        else:
            # 为每个节点设置颜色，中心节点用特殊颜色
            node_colors = []
            for node in subgraph.nodes():
                if node == node_idx:
                    node_colors.append('#FF0000')  # 中心节点用红色
                else:
                    node_colors.append('#E6F3FF')
            cmap = None
            vmin, vmax = None, None  # 修复变量作用域问题
            node_sizes = np.ones(len(subgraph.nodes())) * 100
            if node_idx is not None and node_idx in subgraph.nodes():
                node_sizes[list(subgraph.nodes()).index(node_idx)] = 400
        
        # 绘制节点
        nodes = nx.draw_networkx_nodes(
            subgraph, pos,
            node_color=node_colors,
            node_size=node_sizes,
            cmap=cmap,
            vmin=vmin,
            vmax=vmax,
            alpha=0.9,
            linewidths=1,
            edgecolors='#666666',
            ax=ax
        )
        
        # 绘制边
        edge_colors = plt.cm.Reds(final_weights)
        edges = nx.draw_networkx_edges(
            subgraph, pos,
            edge_color=edge_colors,
            width=1.5 + 4 * final_weights,
            alpha=0.6 + 0.4 * final_weights,
            arrowsize=20,
            ax=ax
        )
        
        # 添加标签
        if show_node_labels:
            # 显示节点索引
            nx.draw_networkx_labels(subgraph, pos,
                                  font_size=font_size,
                                  font_weight='bold',
                                  font_color='black',
                                  ax=ax)
        else:
            # 显示节点类型
            labels = {node: str(node_types[node]) for node in subgraph.nodes()}
            nx.draw_networkx_labels(subgraph, pos,
                                  labels=labels,
                                  font_size=font_size,
                                  font_weight='bold',
                                  font_color='black',
                                  ax=ax)
        
        # 添加颜色条
        if node_mask is not None:
            sm1 = plt.cm.ScalarMappable(cmap=node_cmap, norm=plt.Normalize(vmin=0, vmax=1))
            sm1.set_array([])
            cbar1 = plt.colorbar(sm1, label='Node Feature Importance', 
                               shrink=0.5, pad=0.02, ax=ax)
            cbar1.ax.tick_params(labelsize=font_size-2)
        
        sm2 = plt.cm.ScalarMappable(cmap=plt.cm.Reds, norm=plt.Normalize(vmin=0, vmax=1))
        sm2.set_array([])
        cbar2 = plt.colorbar(sm2, label='Edge Importance', 
                            shrink=0.5, pad=0.02, ax=ax)
        cbar2.ax.tick_params(labelsize=font_size-2)
        
        # 添加网格
        ax.grid(True, linestyle='--', alpha=0.3)
        
        # 设置标题
        if show_node_labels:
            ax.set_title("Node Index View", fontsize=font_size+2, pad=20)
        else:
            ax.set_title("Node Type View", fontsize=font_size+2, pad=20)
    
    # 绘制两个子图
    draw_subgraph(ax1, show_node_labels=True)
    draw_subgraph(ax2, show_node_labels=False)
    
    # 添加总标题
    if edge_threshold is not None:
        title = f"Explanation Graph (Edge Threshold={edge_threshold})"
    else:
        title = f"Explanation Graph (Top {top_edge_percent}% Edges"
        if node_mask is not None:
            title += f", Top {top_node_percent}% Nodes"
        title += ")"

    title += f"\n{len(subgraph.nodes())} nodes, {len(subgraph.edges())} edges"
    if node_idx is not None:
        title += f", Center: {node_idx}"
    plt.suptitle(title, fontsize=font_size+4, y=1.02)
    
    # 调整布局
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, bbox_inches='tight', dpi=dpi,
                   facecolor='white', edgecolor='none')
    plt.show()


def visualize_high_impact_explanation(graph, edge_mask, node_mask=None, node_idx=None,
                                    impact_level='low', save_path=None, layout='spring'):
    """
    便捷函数：根据影响级别自动设置过滤参数

    Parameters
    ----------
    graph : DGLGraph
        输入图
    edge_mask : Tensor
        边重要性掩码
    node_mask : Tensor, optional
        节点重要性掩码
    node_idx : int, optional
        中心节点索引
    impact_level : str, optional
        影响级别 ('high'|'medium'|'low')
        - 'high': 只显示最重要的5%边和10%节点
        - 'medium': 显示重要的15%边和25%节点
        - 'low': 显示30%边和40%节点
    save_path : str, optional
        保存路径
    layout : str, optional
        布局算法
    """
    impact_configs = {
        'high': {'edge_percent': 5, 'node_percent': 10},
        'medium': {'edge_percent': 15, 'node_percent': 25},
        'low': {'edge_percent': 80, 'node_percent': 80}
    }

    config = impact_configs.get(impact_level, impact_configs['medium'])

    print(f"使用 {impact_level} 影响级别过滤：")
    print(f"- 边过滤：前 {config['edge_percent']}%")
    print(f"- 节点过滤：前 {config['node_percent']}%")

    visualize_explanation(
        graph=graph,
        edge_mask=edge_mask,
        node_mask=node_mask,
        node_idx=node_idx,
        top_edge_percent=config['edge_percent'],
        top_node_percent=config['node_percent'],
        save_path=save_path,
        layout=layout
    )


def simple_high_impact_viz(graph, edge_mask, node_mask=None, node_idx=None,
                          top_edges=10, top_nodes=15, save_path=None, original_target_id=None):
    """
    简单高影响力可视化 - 只显示最重要的几个边和节点

    Parameters
    ----------
    graph : DGLGraph
        输入图 (通常是解释器返回的子图)
    edge_mask : Tensor
        边重要性掩码
    node_mask : Tensor, optional
        节点重要性掩码
    node_idx : int, optional
        中心节点索引 (在子图中的索引)
    top_edges : int, optional
        显示前几条最重要的边 (默认10)
    top_nodes : int, optional
        显示前几个最重要的节点 (默认15)
    save_path : str, optional
        保存路径
    """
    import networkx as nx
    import matplotlib.pyplot as plt
    import numpy as np

    print(f"=== 简单高影响力可视化 ===")
    print(f"只显示前 {top_edges} 条重要边和前 {top_nodes} 个重要节点")

    # 检查是否有原始节点ID映射
    has_orig_mapping = '_ID' in graph.ndata
    if has_orig_mapping:
        orig_node_ids = graph.ndata['_ID'].detach().cpu().numpy()
        print("使用原始节点ID进行显示")
    else:
        print("使用子图节点ID进行显示")

    # 转换为numpy
    edge_weights = edge_mask.detach().cpu().numpy()
    src, dst = graph.edges()

    # 确定中心节点的原始ID
    if node_idx is not None and has_orig_mapping:
        center_orig_id = orig_node_ids[node_idx]
    elif node_idx is not None:
        center_orig_id = node_idx
    else:
        center_orig_id = None

    print(f"构建以节点 {center_orig_id} 为中心的连通子图")

    # 构建连通子图：使用BFS从中心节点开始扩展
    import networkx as nx

    # 创建完整的图结构用于连通性分析
    full_graph = nx.Graph()
    edge_weight_map = {}  # 存储边权重

    for idx in range(len(edge_weights)):
        u, v = src[idx].item(), dst[idx].item()
        if has_orig_mapping:
            u_orig = orig_node_ids[u]
            v_orig = orig_node_ids[v]
        else:
            u_orig = u
            v_orig = v

        full_graph.add_edge(u_orig, v_orig)
        edge_weight_map[(u_orig, v_orig)] = edge_weights[idx]
        edge_weight_map[(v_orig, u_orig)] = edge_weights[idx]  # 无向图

    # 从中心节点开始BFS扩展，构建连通子图
    if center_orig_id not in full_graph:
        print(f"警告：中心节点 {center_orig_id} 不在图中")
        # 回退到原来的逻辑
        num_edges = len(edge_weights)
        actual_top_edges = min(top_edges, num_edges)
        top_edge_indices = np.argsort(edge_weights)[-actual_top_edges:]

        important_nodes = set()
        important_edges = []
        important_edge_weights = []

        for idx in top_edge_indices:
            u, v = src[idx].item(), dst[idx].item()
            if has_orig_mapping:
                u_orig = orig_node_ids[u]
                v_orig = orig_node_ids[v]
                important_edges.append((u_orig, v_orig))
                important_nodes.add(u_orig)
                important_nodes.add(v_orig)
            else:
                important_edges.append((u, v))
                important_nodes.add(u)
                important_nodes.add(v)
            important_edge_weights.append(edge_weights[idx])
    else:
        # BFS扩展连通子图，优先选择与中心节点直接相连的重要边
        visited_nodes = {center_orig_id}

        # 首先收集与中心节点直接相连的边
        direct_edges = []
        for node in full_graph.neighbors(center_orig_id):
            edge = (center_orig_id, node) if (center_orig_id, node) in edge_weight_map else (node, center_orig_id)
            weight = edge_weight_map[edge]
            direct_edges.append((edge, weight))

        # 按权重排序直接连接的边
        direct_edges.sort(key=lambda x: x[1], reverse=True)

        important_edges = []
        important_edge_weights = []
        important_nodes = {center_orig_id}

        # 首先添加与中心节点直接相连的最重要边
        edges_added = 0
        max_edges = top_edges
        min_direct_edges = min(max(1, max_edges // 2), len(direct_edges))  # 至少选择一半的边是直接连接的

        print(f"优先选择与中心节点直接相连的 {min_direct_edges} 条重要边")

        for i in range(min(min_direct_edges, len(direct_edges))):
            (u, v), weight = direct_edges[i]
            important_edges.append((u, v))
            important_edge_weights.append(weight)
            important_nodes.add(u)
            important_nodes.add(v)
            visited_nodes.add(u)
            visited_nodes.add(v)
            edges_added += 1

        # 如果还需要更多边，继续BFS扩展
        candidate_edges = []

        # 收集当前已访问节点的邻居边
        for visited_node in visited_nodes:
            if visited_node in full_graph:
                for neighbor in full_graph.neighbors(visited_node):
                    if neighbor not in visited_nodes:
                        edge = (visited_node, neighbor) if (visited_node, neighbor) in edge_weight_map else (neighbor, visited_node)
                        weight = edge_weight_map[edge]
                        candidate_edges.append((edge, weight))

        # 去重并按权重排序
        candidate_edges = list(set(candidate_edges))
        candidate_edges.sort(key=lambda x: x[1], reverse=True)

        # 继续添加边直到达到目标数量
        while edges_added < max_edges and candidate_edges:
            # 选择权重最高的边
            (u, v), weight = candidate_edges.pop(0)

            # 添加边和节点
            important_edges.append((u, v))
            important_edge_weights.append(weight)
            important_nodes.add(u)
            important_nodes.add(v)
            edges_added += 1

            # 如果添加了新节点，将其邻居边加入候选列表
            new_nodes = {u, v} - visited_nodes
            for new_node in new_nodes:
                visited_nodes.add(new_node)
                if new_node in full_graph:
                    for neighbor in full_graph.neighbors(new_node):
                        if neighbor not in visited_nodes:
                            edge = (new_node, neighbor) if (new_node, neighbor) in edge_weight_map else (neighbor, new_node)
                            weight = edge_weight_map[edge]
                            # 避免重复添加
                            if (edge, weight) not in candidate_edges:
                                candidate_edges.append((edge, weight))

                # 重新排序候选边
                candidate_edges.sort(key=lambda x: x[1], reverse=True)

        print(f"构建了包含 {len(important_nodes)} 个节点和 {len(important_edges)} 条边的连通子图")
        print(f"所有节点都与中心节点 {center_orig_id} 连通")

        # 统计直接连接和间接连接的边数
        direct_edges = sum(1 for u, v in important_edges if u == center_orig_id or v == center_orig_id)
        indirect_edges = len(important_edges) - direct_edges
        print(f"其中直接连接中心节点的边: {direct_edges} 条，间接连接的边: {indirect_edges} 条")

    # 如果有节点掩码，进一步筛选节点，但要确保有足够的节点
    if node_mask is not None:
        node_weights = node_mask.detach().cpu().numpy()
        # 获取前top_nodes个最重要的节点
        actual_top_nodes = min(top_nodes, len(node_weights))
        top_node_indices = np.argsort(node_weights)[-actual_top_nodes:]

        # 如果有原始ID映射，转换为原始ID
        if has_orig_mapping:
            important_nodes_from_mask = set(orig_node_ids[top_node_indices])
        else:
            important_nodes_from_mask = set(top_node_indices)

        # 取并集而不是交集，确保有足够的节点
        final_nodes = important_nodes.union(important_nodes_from_mask)

        # 处理中心节点 - 确保中心节点始终包含
        if node_idx is not None:
            if has_orig_mapping:
                center_orig_id = orig_node_ids[node_idx]
                final_nodes.add(center_orig_id)
                print(f"确保中心节点 {center_orig_id} 包含在显示中")
            else:
                final_nodes.add(node_idx)
                print(f"确保中心节点 {node_idx} 包含在显示中")
    else:
        final_nodes = important_nodes
        if node_idx is not None:
            if has_orig_mapping:
                center_orig_id = orig_node_ids[node_idx]
                final_nodes.add(center_orig_id)
                print(f"确保中心节点 {center_orig_id} 包含在显示中")
            else:
                final_nodes.add(node_idx)
                print(f"确保中心节点 {node_idx} 包含在显示中")

    # 只保留连接最终节点的边
    final_edges = []
    final_weights = []
    for i, (u, v) in enumerate(important_edges):
        if u in final_nodes and v in final_nodes:
            final_edges.append((u, v))
            final_weights.append(important_edge_weights[i])

    # 如果没有边，至少保留一些重要的边
    if len(final_edges) == 0:
        print("没有边连接重要节点，保留所有重要边")
        final_edges = important_edges
        final_weights = important_edge_weights
        # 更新节点集合
        for u, v in final_edges:
            final_nodes.add(u)
            final_nodes.add(v)

    print(f"最终显示: {len(final_nodes)} 个节点, {len(final_edges)} 条边")

    # 验证中心节点是否在最终节点集合中
    if node_idx is not None and has_orig_mapping:
        center_orig_id = orig_node_ids[node_idx]
        if center_orig_id in final_nodes:
            print(f"✓ 中心节点 {center_orig_id} 确实在最终显示的节点中")
        else:
            print(f"✗ 警告：中心节点 {center_orig_id} 不在最终显示的节点中")

    print(f"最终显示的节点列表: {sorted(list(final_nodes))}")

    # 创建子图
    subgraph = nx.Graph()
    subgraph.add_nodes_from(final_nodes)
    subgraph.add_edges_from(final_edges)

    # 绘图
    plt.figure(figsize=(12, 8), dpi=150)
    pos = nx.spring_layout(subgraph, k=2, iterations=50)

    # 节点颜色和大小
    node_colors = []
    node_sizes = []

    # 确定中心节点的显示ID
    if node_idx is not None:
        if has_orig_mapping:
            center_display_id = orig_node_ids[node_idx]
        else:
            center_display_id = node_idx
    else:
        center_display_id = None

    for node in subgraph.nodes():
        if node == center_display_id:
            node_colors.append('#FF4444')  # 中心节点红色
            node_sizes.append(800)
        else:
            node_colors.append('#4488FF')  # 其他节点蓝色
            node_sizes.append(400)

    # 绘制节点
    nx.draw_networkx_nodes(subgraph, pos,
                          node_color=node_colors,
                          node_size=node_sizes,
                          alpha=0.8,
                          linewidths=2,
                          edgecolors='black')

    # 绘制边，边的粗细和颜色反映重要性
    final_weights = np.array(final_weights)
    edge_widths = 1 + 8 * (final_weights - final_weights.min()) / (final_weights.max() - final_weights.min() + 1e-8)
    edge_colors = plt.cm.Reds(0.3 + 0.7 * (final_weights - final_weights.min()) / (final_weights.max() - final_weights.min() + 1e-8))

    nx.draw_networkx_edges(subgraph, pos,
                          edge_color=edge_colors,
                          width=edge_widths,
                          alpha=0.7)

    # 添加节点标签 - 显示原始节点索引，使用黑色字体
    # 创建标签字典，确保显示原始节点ID
    node_labels = {node: str(node) for node in subgraph.nodes()}
    nx.draw_networkx_labels(subgraph, pos,
                           labels=node_labels,
                           font_size=10,
                           font_weight='bold',
                           font_color='black')

    # 添加边权重标签（只显示最重要的几条边）
    if len(final_edges) <= 15:  # 边不太多时显示权重
        edge_labels = {}
        for i, (u, v) in enumerate(final_edges):
            edge_labels[(u, v)] = f'{final_weights[i]:.3f}'
        nx.draw_networkx_edge_labels(subgraph, pos, edge_labels, font_size=8)

    # 显示标题，优先使用原始目标节点ID
    if original_target_id is not None:
        title_node_id = original_target_id
    else:
        title_node_id = center_display_id if center_display_id is not None else "Unknown"
    plt.title(f'High Impact Explanation for Node {title_node_id}\n'
              f'{len(final_nodes)} nodes, {len(final_edges)} edges',
              fontsize=14, pad=20)
    plt.axis('off')
    plt.tight_layout()

    if save_path:
        plt.savefig(save_path, bbox_inches='tight', dpi=150,
                   facecolor='white', edgecolor='none')
        print(f"图片已保存到: {save_path}")

    plt.show()


def quick_viz(graph, edge_mask, node_mask=None, node_idx=None, original_target_id=None, save_path=None):
    """
    最简洁的可视化 - 只显示最核心的影响元素

    Parameters
    ----------
    graph : DGLGraph
        输入图
    edge_mask : Tensor
        边重要性掩码
    node_mask : Tensor, optional
        节点重要性掩码
    node_idx : int, optional
        中心节点索引（在子图中的索引）
    original_target_id : int, optional
        原始目标节点ID（用于显示标题）
    save_path : str, optional
        保存路径
    """
    import numpy as np

    print("=== 最简洁可视化 ===")

    # 检查是否有原始节点ID映射
    has_orig_mapping = '_ID' in graph.ndata
    if has_orig_mapping:
        orig_node_ids = graph.ndata['_ID'].detach().cpu().numpy()
        print("显示原始节点ID")
    else:
        print("显示子图节点ID")

    # 确定中心节点的原始ID
    if node_idx is not None and has_orig_mapping:
        center_orig_id = orig_node_ids[node_idx]
    elif node_idx is not None:
        center_orig_id = node_idx
    else:
        center_orig_id = None

    # 打印最重要的边信息，优先显示与中心节点相连的边
    edge_weights = edge_mask.detach().cpu().numpy()
    src, dst = graph.edges()

    # 找到与中心节点相连的边
    center_connected_edges = []
    other_edges = []

    for idx in range(len(edge_weights)):
        u, v = src[idx].item(), dst[idx].item()
        if has_orig_mapping:
            u_display = orig_node_ids[u]
            v_display = orig_node_ids[v]
        else:
            u_display = u
            v_display = v

        edge_info = (idx, u_display, v_display, edge_weights[idx])

        # 检查是否与中心节点相连
        if center_orig_id is not None and (u_display == center_orig_id or v_display == center_orig_id):
            center_connected_edges.append(edge_info)
        else:
            other_edges.append(edge_info)

    # 按重要性排序
    center_connected_edges.sort(key=lambda x: x[3], reverse=True)
    other_edges.sort(key=lambda x: x[3], reverse=True)

    # 打印结果 - 显示连通子图中的边
    print(f"\n连通子图中的重要边 (以节点 {center_orig_id} 为中心):")

    # 将所有边按重要性排序
    all_edges = center_connected_edges + other_edges
    all_edges.sort(key=lambda x: x[3], reverse=True)

    # 显示前5条最重要的边
    display_count = min(5, len(all_edges))
    for i in range(display_count):
        _, u_display, v_display, weight = all_edges[i]
        # 标记是否直接连接到中心节点
        is_center_connected = (u_display == center_orig_id or v_display == center_orig_id)
        connection_type = " [直连中心]" if is_center_connected else " [间接连接]"
        print(f"  {i+1}. 边 ({u_display}, {v_display}) - 重要性: {weight:.4f}{connection_type}")

    if len(all_edges) == 0:
        print("  (没有找到重要边)")

    # 如果有节点掩码，打印最重要的节点信息
    if node_mask is not None:
        node_weights = node_mask.detach().cpu().numpy()
        top_5_node_indices = np.argsort(node_weights)[-5:]
        print("\n前5个最重要的节点:")
        for i, idx in enumerate(reversed(top_5_node_indices)):
            if has_orig_mapping:
                node_display = orig_node_ids[idx]
            else:
                node_display = idx
            print(f"  {i+1}. 节点 {node_display} - 重要性: {node_weights[idx]:.4f}")

    # 调用简单可视化，但只显示最少的元素
    simple_high_impact_viz(
        graph=graph,
        edge_mask=edge_mask,
        node_mask=node_mask,
        node_idx=node_idx,
        top_edges=5,  # 只显示前5条边
        top_nodes=8,  # 只显示前8个节点
        save_path=save_path,
        original_target_id=original_target_id  # 传递原始目标节点ID
    )


def low_impact_viz(graph, edge_mask, node_mask=None, node_idx=None, save_path=None):
    """
    快速高影响力可视化 - 使用简单版本

    Parameters
    ----------
    graph : DGLGraph
        输入图
    edge_mask : Tensor
        边重要性掩码
    node_mask : Tensor, optional
        节点重要性掩码
    node_idx : int, optional
        中心节点索引
    save_path : str, optional
        保存路径
    """
    simple_high_impact_viz(
        graph=graph,
        edge_mask=edge_mask,
        node_mask=node_mask,
        node_idx=node_idx,
        top_edges=10,
        top_nodes=15,
        save_path=save_path
    )


def main(main_args):
    device = main_args.device if main_args.device >= 0 else "cpu"
    device = torch.device(device)
    dataset_name = main_args.dataset
    main_args.num_hidden = 64
    main_args.num_layers = 3
    metadata = load_metadata(dataset_name)
    main_args.n_dim = metadata['node_feature_dim']
    main_args.e_dim = metadata['edge_feature_dim']
    model = build_model(main_args)
    model.load_state_dict(torch.load("./checkpoints/checkpoint-{}.pt".format(dataset_name), map_location=device))
    model = model.to(device)
    
    # 确保模型参数需要梯度
    for param in model.parameters():
        param.requires_grad = True
    
    # 创建包装器
    wrapped_model = GATExplainerWrapper(model)
    wrapped_model.eval()

    # 创建解释器
    explainer = GNNExplainer(
        model=wrapped_model,
        # num_hops=main_args.num_layers,
        num_hops=3,
        lr=0.01,
        alpha1=0.01, alpha2=1.5, beta1=0.5, beta2=0.1,
        num_epochs=200,
    )
    
    # 加载数据
    g = load_entity_level_dataset(dataset_name, 'train', 3, True).to(device)
    
    # 对特定节点进行解释
    # process_idx = (g.ndata['type']==0).nonzero().squeeze()
    # node_idx = process_idx[120].item()  # 选择要解释的节点   30 ok
    node_idx = 1063
    new_center, subgraph, node_feat_mask, edge_mask = explainer.explain_node(node_idx, g, g.ndata['attr'])
    # 打印解释结果
    print('中心节点：',new_center)
    print(f"节点特征掩码形状: {node_feat_mask.shape}")
    print(f"边掩码形状: {edge_mask.shape}")
    print(f"子图节点数: {subgraph.num_nodes()}")
    print(f"子图边数: {subgraph.num_edges()}")

    # 检查子图是否有节点映射信息
    print("子图属性:", subgraph.ndata.keys())
    if hasattr(subgraph, 'ndata') and '_ID' in subgraph.ndata:
        print("子图有原始节点ID映射")
        orig_node_ids = subgraph.ndata['_ID'].detach().cpu().numpy()
        print("原始节点ID范围:", orig_node_ids.min(), "-", orig_node_ids.max())
        print("中心节点在子图中的索引:", new_center.item())
        print("中心节点的原始ID:", orig_node_ids[new_center.item()])
        print("目标节点1063是否在子图中:", 1063 in orig_node_ids)
    else:
        print("子图没有原始节点ID映射，使用子图节点ID")
    
    # 高影响力可视化解释结果
    print(f"边掩码范围：{edge_mask.min().item():.4f} - {edge_mask.max().item():.4f}")
    if node_feat_mask is not None:
        print(f"节点掩码范围：{node_feat_mask.min().item():.4f} - {node_feat_mask.max().item():.4f}")

    # 使用最简洁的可视化
    print("\n=== 使用简洁可视化查看最重要的影响 ===")
    print(f"原始目标节点: {node_idx}, 子图中心节点索引: {new_center.item()}")
    quick_viz(
        graph=subgraph,
        edge_mask=edge_mask,
        node_mask=node_feat_mask,
        node_idx=new_center.item(),  # 传递子图中的索引
        original_target_id=node_idx,  # 传递原始目标节点ID用于显示
        save_path=f"explanation_simple_node_{node_idx}.png"
    )

if __name__ == '__main__':
    args = build_args()
    main(args)
