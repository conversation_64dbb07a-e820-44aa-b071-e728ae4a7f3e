import psycopg2
from psycopg2 import sql
from psycopg2 import errors

def init_database_connection(db_name='tc_theia_dataset_db', user='postgres', password='123456', host='localhost', port=5432):
    """
    初始化数据库连接并返回游标和连接对象
    如果数据库或表不存在则自动创建
    
    参数:
        db_name: 数据库名称
        user: PostgreSQL 用户名
        password: PostgreSQL 密码
        host: 数据库主机
        port: 数据库端口
    
    返回:
        tuple: (游标, 连接) 对象
    """
    try:
        # 首先连接到 postgres 数据库以检查/创建目标数据库
        admin_conn = psycopg2.connect(
            database='postgres',
            user=user,
            password=password,
            host=host,
            port=port
        )
        admin_conn.autocommit = True  # 创建数据库需要自动提交
        admin_cur = admin_conn.cursor()
        
        # 检查数据库是否存在，不存在则创建
        try:
            admin_cur.execute(sql.SQL("CREATE DATABASE {}").format(
                sql.Identifier(db_name)))
            print(f"数据库 {db_name} 创建成功")
        except errors.DuplicateDatabase:
            print(f"数据库 {db_name} 已存在，直接连接")
        
        admin_cur.close()
        admin_conn.close()
        
        # 连接到目标数据库
        conn = psycopg2.connect(
            database=db_name,
            user=user,
            password=password,
            host=host,
            port=port
        )
        cur = conn.cursor()
        
        return cur, conn
        
    except Exception as e:
        print(f"数据库连接失败: {e}")
        raise

def create_tables(cursor, connection, event_table_names=None):
    """
    创建所有需要的表，支持自定义事件表名称
    
    参数:
        cursor: 数据库游标
        connection: 数据库连接
        event_table_names: 事件表名称列表，如 ['event_table', 'event_archive', 'event_backup']
                          默认为 None，表示只创建默认的 event_table
    """
    try:
        # 创建 file_node_table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS file_node_table (
                node_uuid varchar not null,
                path      varchar,
                index_id      int,
                constraint file_node_table_pk
                    primary key (node_uuid)
            )
        """)
        cursor.execute("ALTER TABLE file_node_table OWNER TO postgres")
        
        # 创建 netflow_node_table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS netflow_node_table (
                node_uuid varchar not null,
                src_addr  varchar,
                src_port  varchar,
                dst_addr  varchar,
                dst_port  varchar,
                index_id    int,
                constraint netflow_node_table_pk
                    primary key (node_uuid)
            )
        """)
        cursor.execute("ALTER TABLE netflow_node_table OWNER TO postgres")
        
        # 创建 subject_node_table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS subject_node_table (
                node_uuid varchar not null,
                path varchar,
                cmd varchar,
                index_id    int,
                constraint subject_node_table_pk
                    primary key (node_uuid)
            )
        """)
        cursor.execute("ALTER TABLE subject_node_table OWNER TO postgres")
        
        # # 创建 node2id 表
        # cursor.execute("""
        #     CREATE TABLE IF NOT EXISTS node2id (
        #         hash_id   varchar not null
        #             constraint node2id_pk
        #                 primary key,
        #         node_type varchar,
        #         msg       varchar,
        #         index_id  bigint
        #     )
        # """)
        # cursor.execute("ALTER TABLE node2id OWNER TO postgres")
        # cursor.execute("CREATE UNIQUE INDEX IF NOT EXISTS node2id_hash_id_uindex ON node2id (hash_id)")
        
        # 处理事件表名称
        if event_table_names is None:
            event_table_names = ['event_table']  # 默认只创建 event_table
        
        # 创建多个事件表
        for table_name in event_table_names:
            cursor.execute(sql.SQL("""
                CREATE TABLE IF NOT EXISTS {} (
                    src_node      varchar,
                    src_name      varchar,
                    src_index_id  int,
                    src_type  varchar,
                    operation     varchar,
                    event_id     varchar,
                    dst_node      varchar,
                    dst_name      varchar,
                    dst_index_id  int,
                    dst_type      varchar,
                    timestamp_rec bigint,
                    _id           serial
                )
            """).format(sql.Identifier(table_name)))
            
            cursor.execute(sql.SQL("ALTER TABLE {} OWNER TO postgres").format(sql.Identifier(table_name)))
            
            cursor.execute(sql.SQL("""
                CREATE UNIQUE INDEX IF NOT EXISTS {} ON {} (_id)
            """).format(
                sql.Identifier(f"{table_name}__id_uindex"),
                sql.Identifier(table_name)
            ))
            
            cursor.execute(sql.SQL("""
                GRANT DELETE, INSERT, REFERENCES, SELECT, TRIGGER, TRUNCATE, UPDATE
                ON {} TO postgres
            """).format(sql.Identifier(table_name)))
            
            print(f"表 {table_name} 创建/验证成功")
        
        connection.commit()
        print("所有表创建/验证完成")
        
    except Exception as e:
        connection.rollback()
        print(f"创建表时出错: {e}")
        raise

# 使用示例
if __name__ == "__main__":
    try:
        # 初始化数据库连接
        cursor, conn = init_database_connection()
        
        # 示例1：只创建默认的 event_table
        # create_tables(cursor, conn)
        
        # 示例2：创建自定义名称的表
        custom_tables = ['event_table_6r_0', 'event_table_6r_1', 'event_table_6r_2', 'event_table_6r_3', 'event_table_6r_8']
        create_tables(cursor, conn, event_table_names=custom_tables)
        
        # 使用完成后关闭连接
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"初始化过程中出错: {e}")