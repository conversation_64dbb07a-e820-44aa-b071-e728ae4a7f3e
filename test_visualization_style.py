#!/usr/bin/env python3
"""
测试可视化样式的脚本
验证节点标签颜色和标题语言
"""

import torch
import matplotlib.pyplot as plt
from explain import build_model, GATExplainerWrapper, quick_viz
from dgl.nn.pytorch.explain import GNNExplainer
from utils.loaddata import load_metadata, load_entity_level_dataset
from utils.config import build_args

def test_visualization_style():
    """测试可视化样式"""
    print("=== 测试可视化样式 ===")
    
    # 设置参数
    args = build_args()
    args.dataset = 'theia'
    args.device = 0
    
    device = torch.device(args.device)
    dataset_name = args.dataset
    args.num_hidden = 64
    args.num_layers = 3
    
    # 加载模型
    metadata = load_metadata(dataset_name)
    args.n_dim = metadata['node_feature_dim']
    args.e_dim = metadata['edge_feature_dim']
    model = build_model(args)
    model.load_state_dict(torch.load("./checkpoints/checkpoint-{}.pt".format(dataset_name), map_location=device))
    model = model.to(device)
    
    # 创建解释器
    wrapped_model = GATExplainerWrapper(model)
    wrapped_model.eval()
    
    explainer = GNNExplainer(
        model=wrapped_model,
        num_hops=3,
        lr=0.01,
        alpha1=0.01, alpha2=1.5, beta1=0.5, beta2=0.1,
        num_epochs=50,  # 减少epochs以加快测试
    )
    
    # 加载数据
    g = load_entity_level_dataset(dataset_name, 'train', 3, True).to(device)
    
    # 测试节点
    node_idx = 1063
    print(f"测试节点: {node_idx}")
    
    # 生成解释
    new_center, subgraph, node_feat_mask, edge_mask = explainer.explain_node(node_idx, g, g.ndata['attr'])
    
    print(f"子图节点数: {subgraph.num_nodes()}")
    print(f"子图边数: {subgraph.num_edges()}")
    print(f"中心节点在子图中的索引: {new_center.item()}")
    
    # 检查原始ID映射
    if '_ID' in subgraph.ndata:
        orig_node_ids = subgraph.ndata['_ID'].detach().cpu().numpy()
        center_orig_id = orig_node_ids[new_center.item()]
        print(f"中心节点的原始ID: {center_orig_id}")
        assert center_orig_id == node_idx, f"原始ID不匹配: {center_orig_id} != {node_idx}"
        print("✓ 原始ID映射正确")
    
    # 测试可视化
    print("\n=== 测试可视化样式 ===")
    quick_viz(
        graph=subgraph,
        edge_mask=edge_mask,
        node_mask=node_feat_mask,
        node_idx=new_center.item(),
        original_target_id=node_idx,
        save_path=f"test_style_node_{node_idx}.png"
    )
    
    print("\n=== 样式验证要点 ===")
    print("1. 节点标签应该是黑色字体")
    print("2. 标题应该是英文: 'High Impact Explanation for Node 1063'")
    print("3. 中心节点1063应该是红色大圆圈")
    print("4. 其他节点应该是蓝色中等圆圈")
    print("5. 边的粗细应该反映重要性")
    print(f"6. 图片已保存为: test_style_node_{node_idx}.png")
    
    print("\n✓ 可视化样式测试完成")

if __name__ == '__main__':
    test_visualization_style()
