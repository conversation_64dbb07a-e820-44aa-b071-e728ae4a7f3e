import os
import torch
import random
import numpy as np
import pickle as pkl

from attack_utils import get_poisonable_process, get_connect_num, cal_poison_ratio, structure_analyse

def dynamic_node_selection(cos_sim_scores, orig_idx_tensor, poisonable_idx, 
                          already_selected, need_num, hard_scores, 
                          node_type='SUBJECT_PROCESS', init_threshold=0.0):
    """
    动态阈值策略选择节点
    """
    # 定义阈值级别
    if node_type == 'SUBJECT_PROCESS':
        thresholds = [0.5, 0.3, 0.0, -0.3, -0.5]  # 进程节点阈值级别
    else:
        thresholds = [0.8, 0.7, 0.6, 0.5, 0.4, 0.3]  # 其他节点阈值级别
    
    # 起始位置
    start_idx = 0
    for i, t in enumerate(thresholds):
        if t <= init_threshold:
            start_idx = i
            break
    
    selected = []
    
    for level, threshold in enumerate(thresholds[start_idx:]):
        if len(selected) >= need_num:
            break
            
        # 获取当前阈值下的候选节点
        candidates_mask = cos_sim_scores >= threshold
        candidates_local_idx = candidates_mask.nonzero().squeeze().tolist()
        if isinstance(candidates_local_idx, int):
            candidates_local_idx = [candidates_local_idx]
        
        # 转换为全局索引
        candidates_global = set()
        for local_idx in candidates_local_idx:
            if local_idx < len(orig_idx_tensor):
                global_idx = orig_idx_tensor[local_idx].item()
                candidates_global.add(global_idx)
        
        # 过滤：可投毒 & 未选择
        candidates_global = candidates_global.intersection(set(poisonable_idx))
        candidates_global = candidates_global - set(already_selected)
        candidates_global = candidates_global - set(selected)
        
        if not candidates_global:
            continue
        
        # 计算需要从这个级别选择的数量
        still_need = need_num - len(selected)
        
        if len(candidates_global) <= still_need:
            # 全部选择
            selected.extend(list(candidates_global))
        else:
            # 需要根据综合分数排序选择
            # 动态计算权重：随着阈值降低，难样本权重增加
            sim_weight = max(0.3, 0.9 - level * 0.15)  # 从0.9逐渐降到0.3
            hard_weight = 1 - sim_weight
            
            # 计算综合分数
            scored_candidates = []
            for global_idx in candidates_global:
                # 找到local_idx
                local_idx = None
                for li in candidates_local_idx:
                    if li < len(orig_idx_tensor) and orig_idx_tensor[li].item() == global_idx:
                        local_idx = li
                        break
                
                if local_idx is not None:
                    sim_score = cos_sim_scores[local_idx].item()
                    hard_score = hard_scores.get(global_idx, 0.5)
                    combined_score = sim_weight * sim_score + hard_weight * hard_score
                    scored_candidates.append((global_idx, combined_score))
            
            # 排序并选择
            scored_candidates.sort(key=lambda x: x[1], reverse=True)
            selected.extend([x[0] for x in scored_candidates[:still_need]])
    
    return selected

def dynamic_node_selection_with_constraint(cos_sim_scores, orig_idx_tensor, graph,
                                         poisonable_idx, selected_processes,
                                         need_num, need_process_num, most_type,
                                         hard_scores, node_type, init_threshold,
                                         filted_candidates):
    """
    带连接约束的动态选择（用于Socket和File节点）
    """
    # 定义阈值级别
    thresholds = [0.9, 0.8, 0.7, 0.6, 0.5, 0.4, 0.3, 0.2]
    
    # 找到起始阈值位置
    start_idx = 0
    for i, t in enumerate(thresholds):
        if t <= init_threshold:
            start_idx = i
            break
    
    selected_count = 0
    
    for level, threshold in enumerate(thresholds[start_idx:]):
        if selected_count >= need_num:
            break
            
        # 获取当前阈值下的候选
        candidates_mask = cos_sim_scores >= threshold
        candidates_local_idx = candidates_mask.nonzero().squeeze()
        if candidates_local_idx.dim() == 0:
            candidates_local_idx = candidates_local_idx.unsqueeze(0)
        
        # 动态权重
        sim_weight = max(0.3, 0.8 - level * 0.1)
        hard_weight = 1 - sim_weight
        
        # 评分并排序候选节点
        scored_candidates = []
        for local_idx in candidates_local_idx:
            local_idx_item = local_idx.item()
            if local_idx_item >= len(orig_idx_tensor):
                continue
                
            orig_idx = orig_idx_tensor[local_idx_item].item()
            
            # 检查连接约束
            process_num, tmp_process_idx = get_connect_num(
                graph, orig_idx, poisonable_idx, most_type, return_process=True
            )
            
            if process_num >= need_process_num:
                sim_score = cos_sim_scores[local_idx_item].item()
                hard_score = hard_scores.get(orig_idx, 0.5)
                combined_score = sim_weight * sim_score + hard_weight * hard_score
                scored_candidates.append((orig_idx, tmp_process_idx, combined_score))
        
        # 排序
        scored_candidates.sort(key=lambda x: x[2], reverse=True)
        
        # 选择
        for orig_idx, tmp_process_idx, score in scored_candidates:
            if selected_count >= need_num:
                break
                
            # 随机选择需要的进程
            selected_procs = random.sample(tmp_process_idx, need_process_num)
            
            # 更新结果
            for proc_idx in selected_procs:
                if proc_idx not in filted_candidates:
                    filted_candidates[proc_idx] = []
                if proc_idx not in selected_processes:
                    selected_processes.append(proc_idx)
                filted_candidates[proc_idx].append(orig_idx)
            
            selected_count += 1
    
    return selected_count

def choose_poisoning_node(cfg, train_data_all, test_data, malicious_node, sim=True, detector_model=None):
    '''
    sim: 是否考虑拓扑结构相似性
    detector_model: 检测器模型，用于计算节点的难样本分数, None表示不考虑原始特征有效性
    
    选择投毒节点。考虑因素：
    1. 拓扑结构相似性
    2. 原始特征有效性
    3. 投毒可行性
    '''
    candidates_all = {}  # {node_type:[[], [] ...]}
    ratio = cfg.poison_ratio
        
    if os.path.exists(f'./data/{cfg.dataset}/candidates_all.pkl'):
        print('选择已保存投毒节点')
        with open(f'./data/{cfg.dataset}/candidates_all.pkl','rb') as f:
            candidates_all = pkl.load(f)
        cal_poison_ratio(cfg, train_data_all, candidates_all)
        return candidates_all
    
    # 计算各个类型节点所需的数量
    poisoning_num_all = {}  # {node_type:[ , , ,], }
    for node_type in malicious_node.keys():
        poisoning_num_all[node_type] = []
        for g_i in range(len(train_data_all)):
            node_num = len((train_data_all[g_i].ndata['type'] == cfg.node_type_dict[node_type]).nonzero().squeeze())
            poisoning_num_all[node_type].append(int(node_num*ratio[node_type]))
    
    # 可投毒进程的索引
    poisonable_idx_all = get_poisonable_process(cfg, train_data_all)  # [[], [], ...]
    
    # 原始特征的有效性
    hard_scores = {}
    if detector_model is not None:
        print("计算难样本分数...")
        hard_scores = compute_hard_scores(cfg, train_data_all, detector_model)
 
    # 相似性分析
    if sim:    
        if not os.path.exists(f'./data/{cfg.dataset}/cos_sim_all.pkl'):
            cos_sim_all = structure_analyse(cfg, train_data_all, test_data, malicious_node)
        else:
            cos_sim_all = pkl.load(open(f'./data/{cfg.dataset}/cos_sim_all.pkl', 'rb'))
        
        # 各类型节点原始索引
        orig_process_idx_tensor = [(train_data.ndata['type']==cfg.node_type_dict['SUBJECT_PROCESS']).nonzero().squeeze() for train_data in train_data_all]
        orig_socket_idx_tensor = [(train_data.ndata['type']==cfg.node_type_dict['NetFlowObject']).nonzero().squeeze() for train_data in train_data_all]
        orig_file_idx_tensor = [(train_data.ndata['type']==cfg.node_type_dict['FILE_OBJECT_BLOCK']).nonzero().squeeze() for train_data in train_data_all]
        
        # 确保最后处理进程节点
        keys = list(cos_sim_all.keys())
        keys.remove('SUBJECT_PROCESS')
        keys.append('SUBJECT_PROCESS')
        candidates_all['SUBJECT_PROCESS'] = [[] for _ in range(len(train_data_all))]  # 初始化  [ [], [], [] ]
        for node_type in keys:
            cos_sim = cos_sim_all[node_type]['cos_sim']  # [[graph0_tensor],[graph1_tensor], ...]
            similar_groups = cos_sim_all[node_type]['group']
            
            if node_type == 'SUBJECT_PROCESS':
                # 动态选择
                for g_i in range(len(train_data_all)):  # 当前图
                    cur_num = len(candidates_all['SUBJECT_PROCESS'][g_i])
                    need_num = poisoning_num_all[node_type][g_i]
                    if cur_num<need_num:
                        need_num_now = need_num - cur_num
                        print(f'共{need_num}个 差{need_num_now}个进程节点')
                        for group_i, group in enumerate(similar_groups):
                            group_need_num = int(need_num_now*len(group)/len(malicious_node[node_type]))+1
                            
                            # 使用动态策略选择
                            selected = dynamic_node_selection(
                                cos_sim[g_i][group_i],
                                orig_process_idx_tensor[g_i],
                                poisonable_idx_all[g_i],
                                candidates_all['SUBJECT_PROCESS'][g_i],
                                group_need_num,
                                hard_scores.get(g_i, {}),
                                node_type='SUBJECT_PROCESS',
                                init_threshold=0.0  # 进程节点初始阈值
                            )
                            
                            candidates_all[node_type][g_i].extend(selected)
                            print(f'类别{node_type} 图{g_i} group{group_i} 选择了{len(selected)}个节点')
            else:
                # Socket和File节点的处理
                candidates_all[node_type] = []  # 初始化  [ {g1}, {g2}, {...} ]
                if node_type=='NetFlowObject':
                    num_thre = 20
                    count = sum(1 for group in similar_groups if len(group) > num_thre)
                    num_ratio = [1/count if len(group) > num_thre else 0 for group in similar_groups]
                    init_threshold = 0.9
                elif node_type=='FILE_OBJECT_BLOCK':
                    num_ratio = [1/len(similar_groups) for _ in similar_groups]
                    init_threshold = 0.8
                    
                for g_i in range(len(train_data_all)):
                    filted_candidates = {}
                    for group_i, group in enumerate(similar_groups):
                        # need num
                        need_num = int(poisoning_num_all[node_type][g_i]*num_ratio[group_i])
                        if need_num == 0:
                            continue
                            
                        need_process_num, most_type = get_connect_num(test_data, group, malicious_node['SUBJECT_PROCESS'])
                        
                        # 使用动态策略选择满足条件的节点
                        selected_nodes = dynamic_node_selection_with_constraint(
                            cos_sim[g_i][group_i],
                            orig_socket_idx_tensor[g_i] if node_type=='NetFlowObject' else orig_file_idx_tensor[g_i],
                            train_data_all[g_i],
                            poisonable_idx_all[g_i],
                            candidates_all['SUBJECT_PROCESS'][g_i],
                            need_num,
                            need_process_num,
                            most_type,
                            hard_scores.get(g_i, {}),
                            node_type=node_type,
                            init_threshold=init_threshold,
                            filted_candidates=filted_candidates
                        )
                        
                        print(f'类别{node_type} 图{g_i} group{group_i} 需要{need_num}个，选择了{selected_nodes}个节点')
                        
                    candidates_all[node_type].append(filted_candidates)
            print(f'{node_type}选择完毕')
  
    with open(f'./data/{cfg.dataset}/candidates_all.pkl','wb') as f:
        pkl.dump(candidates_all, f)
    cal_poison_ratio(cfg, train_data_all, candidates_all)
    return candidates_all

def compute_hard_scores(cfg, train_data_all, detector_model):
    """
    计算所有节点的难样本分数（0-1之间）
    基于节点在嵌入空间中的离群程度
    """
    hard_scores = []
    
    for g_i, graph in enumerate(train_data_all):
        # 获取节点嵌入
        embeddings = detector_model.embed(graph)
        n_nodes = embeddings.shape[0]
        k = cfg.k_neighbors
        hard_scores[g_i] = []
        
        # 批量计算距离矩阵
        dist_matrix = torch.cdist(embeddings, embeddings, p=2)
        
        for node_idx in range(n_nodes):
            distances = dist_matrix[node_idx]
            distances[node_idx] = float('inf')
            knn_distances, _ = torch.topk(distances, k, largest=False)
            avg_distance = knn_distances.mean().item()
            hard_scores[g_i].append(avg_distance)
        
        # 归一化到0-1
        min_, max_ = min(hard_scores[g_i]), max(hard_scores[g_i])
        hard_scores[g_i] = (hard_scores[g_i] - min_) / (max_ - min_ + 1e-8)
        
        # 过滤入度太大的节点
        in_degrees = graph.in_degrees().cpu()
        filter_mask = in_degrees > cfg.max_in_degree
        filter_indices = filter_mask.nonzero(as_tuple=False).squeeze()
        
        if filter_indices.numel() != 0:
            hard_scores[g_i][filter_indices] = -1
    
    return hard_scores