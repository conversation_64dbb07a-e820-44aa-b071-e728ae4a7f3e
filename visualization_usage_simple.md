# 简洁可视化函数使用说明

## 概述
我已经为 `explain.py` 添加了简洁的可视化函数，专门用于快速查看对目标节点影响最大的边和节点。

## 新增的可视化函数

### 1. `quick_viz()` - 最简洁可视化
**用途**: 快速查看最重要的影响元素，包含文字输出和图形可视化

**特点**:
- 打印前5条最重要的边信息
- 打印前5个最重要的节点信息  
- 只显示前5条边和前8个节点的图形
- 自动处理边数不足的情况

**使用方法**:
```python
quick_viz(
    graph=subgraph,
    edge_mask=edge_mask,
    node_mask=node_feat_mask,
    node_idx=center_node,
    save_path="explanation_simple.png"
)
```

### 2. `simple_high_impact_viz()` - 可定制简洁可视化
**用途**: 可以自定义显示多少条边和多少个节点

**参数**:
- `top_edges`: 显示前几条最重要的边 (默认10)
- `top_nodes`: 显示前几个最重要的节点 (默认15)

**使用方法**:
```python
simple_high_impact_viz(
    graph=subgraph,
    edge_mask=edge_mask,
    node_mask=node_feat_mask,
    node_idx=center_node,
    top_edges=8,      # 只显示前8条边
    top_nodes=12,     # 只显示前12个节点
    save_path="explanation_custom.png"
)
```

## 可视化特点

### 节点显示
- **中心节点**: 红色，大尺寸 (800)
- **其他重要节点**: 蓝色，中等尺寸 (400)
- **节点标签**: 显示节点ID

### 边显示
- **边粗细**: 根据重要性动态调整 (1-9像素)
- **边颜色**: 红色渐变，重要性越高颜色越深
- **边权重标签**: 当边数≤15时显示具体权重值

### 输出信息
```
=== 最简洁可视化 ===

前5条最重要的边:
  1. 边 (742, 2) - 重要性: 0.5497
  2. 边 (740, 10) - 重要性: 0.5472
  3. 边 (748, 2) - 重要性: 0.5438
  4. 边 (741, 2) - 重要性: 0.5436
  5. 边 (748, 739) - 重要性: 0.5318

前5个最重要的节点:
  1. 节点 0 - 重要性: 0.1344
  2. 节点 1 - 重要性: 0.1326
  3. 节点 3 - 重要性: 0.1292
  4. 节点 4 - 重要性: 0.1281
  5. 节点 2 - 重要性: 0.1249

最终显示: 12 个节点, 5 条边
图片已保存到: explanation_simple_node_1063.png
```

## 运行示例

当前 `explain.py` 的主函数已经配置为使用 `quick_viz()`：

```bash
python explain.py --dataset theia --device 0
```

这将：
1. 对节点1063进行解释
2. 显示最重要的边和节点信息
3. 生成简洁的可视化图片
4. 保存为 `explanation_simple_node_1063.png`

## 优势

1. **简洁明了**: 只显示最重要的元素，避免信息过载
2. **快速理解**: 文字输出 + 图形可视化双重展示
3. **自动适应**: 处理边数不足等边界情况
4. **易于定制**: 可以调整显示的边数和节点数
5. **高质量输出**: 150 DPI，适合保存和分享

## 与原有函数的关系

- `visualize_explanation()`: 原有的完整可视化函数，支持百分比过滤
- `visualize_high_impact_explanation()`: 预设的高/中/低影响级别过滤
- `quick_viz()`: 新增的最简洁版本，适合快速查看
- `simple_high_impact_viz()`: 新增的可定制简洁版本

选择哪个函数取决于你的需求：
- 想快速看结果 → 用 `quick_viz()`
- 想自定义显示数量 → 用 `simple_high_impact_viz()`
- 想看更多细节 → 用原有的 `visualize_explanation()`
