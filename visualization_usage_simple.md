# 简洁可视化函数使用说明

## 概述
我已经为 `explain.py` 添加了简洁的可视化函数，专门用于快速查看对目标节点影响最大的边和节点。

## 新增的可视化函数

### 1. `quick_viz()` - 最简洁可视化
**用途**: 快速查看最重要的影响元素，包含文字输出和图形可视化

**特点**:
- 打印连通子图中的重要边信息（区分直连和间接连接）
- 打印前5个最重要的节点信息
- 生成以目标节点为中心的连通子图
- 自动处理边数不足的情况

**参数**:
- `max_nodes`: 最大显示节点数 (默认12)
- `max_edges`: 最大显示边数 (默认8)

**使用方法**:
```python
quick_viz(
    graph=subgraph,
    edge_mask=edge_mask,
    node_mask=node_feat_mask,
    node_idx=center_node,
    save_path="explanation_simple.png",
    max_nodes=15,  # 最多显示15个节点
    max_edges=10   # 最多显示10条边
)
```

### 2. `simple_high_impact_viz()` - 可定制简洁可视化
**用途**: 可以自定义显示多少条边和多少个节点，生成连通子图

**参数**:
- `top_edges`: 连通子图构建时考虑的边数 (默认10)
- `top_nodes`: 从节点特征重要性中选择的节点数 (默认15)
- `max_edges`: 最终显示的最大边数 (默认15)
- `max_nodes`: 最终显示的最大节点数 (默认20)

**使用方法**:
```python
simple_high_impact_viz(
    graph=subgraph,
    edge_mask=edge_mask,
    node_mask=node_feat_mask,
    node_idx=center_node,
    top_edges=15,     # 连通子图中考虑15条边
    top_nodes=20,     # 考虑20个重要节点
    max_edges=12,     # 最多显示12条边
    max_nodes=18,     # 最多显示18个节点
    save_path="explanation_custom.png"
)
```

## 可视化特点

### 节点显示
- **中心节点**: 红色，大尺寸 (800)
- **其他重要节点**: 蓝色，中等尺寸 (400)
- **节点标签**: 显示**原始图中的节点ID**（自动检测并使用 `_ID` 映射）
- **字体颜色**: 黑色，便于阅读

### 边显示
- **边粗细**: 根据重要性动态调整 (1-9像素)
- **边颜色**: 红色渐变，重要性越高颜色越深
- **边权重标签**: 当边数≤15时显示具体权重值

### 图形标题
- **语言**: 英文标题，便于学术使用
- **格式**: `High Impact Explanation for Node {node_id}`
- **信息**: 包含节点数和边数统计

### 输出信息
```
=== 最简洁可视化 ===
显示原始节点ID

前5条最重要的边:
  1. 边 (389168, 763) - 重要性: 0.5359
  2. 边 (389172, 389167) - 重要性: 0.5356
  3. 边 (389175, 389167) - 重要性: 0.5315
  4. 边 (389171, 389167) - 重要性: 0.5298
  5. 边 (389170, 389167) - 重要性: 0.5293

前5个最重要的节点:
  1. 节点 763 - 重要性: 0.1501
  2. 节点 747 - 重要性: 0.1501
  3. 节点 766 - 重要性: 0.1413
  4. 节点 748 - 重要性: 0.1341
  5. 节点 765 - 重要性: 0.1336

最终显示: 12 个节点, 5 条边
图片已保存到: explanation_simple_node_1063.png
```

**注意**: 现在所有的节点和边都显示**原始图中的节点ID**，而不是解释子图中重新编号的ID，这样更容易理解和追踪。

## 运行示例

当前 `explain.py` 的主函数已经配置为使用 `quick_viz()`：

```bash
python explain.py --dataset theia --device 0
```

这将：
1. 对节点1063进行解释
2. 显示最重要的边和节点信息
3. 生成简洁的可视化图片
4. 保存为 `explanation_simple_node_1063.png`

## 优势

1. **简洁明了**: 只显示最重要的元素，避免信息过载
2. **快速理解**: 文字输出 + 图形可视化双重展示
3. **自动适应**: 处理边数不足等边界情况
4. **易于定制**: 可以调整显示的边数和节点数
5. **高质量输出**: 150 DPI，适合保存和分享
6. **原始ID显示**: 自动检测并使用原始图中的节点ID，便于追踪和理解

## 技术说明

### 原始节点ID映射
- 函数会自动检测解释子图中是否有 `_ID` 属性
- 如果有，所有显示的节点ID都会是原始图中的ID
- 如果没有，则使用子图中的重新编号ID
- 这确保了可视化结果与原始数据的一致性

### 连通子图保证
- **连通性保证**：所有显示的节点都与目标节点连通，形成有意义的影响路径
- **优先级策略**：优先选择与目标节点直接相连的重要边，然后通过BFS扩展
- **路径可追踪**：每个节点都可以通过边的路径追溯到目标节点
- **示例输出**：`构建了包含 11 个节点和 10 条边的连通子图`

### 中心节点保证显示
- **目标节点始终显示**：无论目标节点的特征重要性如何，它都会出现在可视化图中
- **红色高亮标记**：目标节点用红色和大尺寸标记，便于识别
- **自动验证**：系统会自动验证目标节点是否在最终显示中，并输出确认信息
- **示例输出**：`✓ 中心节点 1063 确实在最终显示的节点中`

## 与原有函数的关系

- `visualize_explanation()`: 原有的完整可视化函数，支持百分比过滤
- `visualize_high_impact_explanation()`: 预设的高/中/低影响级别过滤
- `quick_viz()`: 新增的最简洁版本，适合快速查看
- `simple_high_impact_viz()`: 新增的可定制简洁版本

选择哪个函数取决于你的需求：
- 想快速看结果 → 用 `quick_viz()`
- 想自定义显示数量 → 用 `simple_high_impact_viz()`
- 想看更多细节 → 用原有的 `visualize_explanation()`
