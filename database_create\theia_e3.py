import os
import re
import torch
from tqdm import tqdm
import hashlib
import psycopg2
from psycopg2 import extras as ex


def store_netflow(file_path, cur, connect, index_id, filelist):
    # Parse data from logs
    netobjset = set()
    netobj2data = {}   # 映射 uuid -> [srcaddr, srcport, dstaddr, dstport]
    for file in tqdm(filelist):
        with open(os.path.join(file_path, file), "r", encoding='utf-8') as f:
            for line in f:
                if "NetFlowObject" in line:
                    try:
                        res = re.findall(
                            'NetFlowObject":{"uuid":"(.*?)"(.*?)"localAddress":"(.*?)","localPort":(.*?),',
                            line)[0]

                        nodeid = res[0]
                        srcaddr = res[2]
                        srcport = res[3]

                        remote = re.findall(
                            '"remoteAddress":"(.*?)","remotePort":(.*?),',
                            line
                        )
                        if len(remote) > 0:
                            dstaddr = res[0][0]
                            dstport = res[0][1]
                        else:
                            dstaddr = None
                            dstport = None

                        # nodeproperty = srcaddr + "," + srcport + "," + str(dstaddr) + "," + str(dstport)
                        netobj2data[nodeid] = [srcaddr, srcport, dstaddr, dstport]
                        netobjset.add(nodeid)  # 记录所有 uuid
                    except:
                        pass

    # Store data into database
    datalist = []

    for uuid in netobj2data.keys():
        srcaddr, srcport, dstaddr, dstport = netobj2data[uuid]
        datalist.append([
            uuid,          # 原始 uuid（不再哈希化）
            srcaddr, 
            srcport, 
            dstaddr, 
            dstport, 
            index_id
        ])
        index_id += 1

    sql = '''insert into netflow_node_table
                            values %s
            '''
    ex.execute_values(cur, sql, datalist, page_size=10000)
    connect.commit()

    return index_id, netobj2data  #  [srcaddr, srcport, dstaddr, dstport]

def store_subject(file_path, cur, connect, index_id, filelist):
    # Parse data from logs
    subject_data = {}  # uuid -> [path, cmdLine]
    subject_uuids = set()  # 存储所有唯一的 uuid

    for file in tqdm(filelist):
        with open(os.path.join(file_path, file), "r", encoding='utf-8') as f:
            for line in f:
                if "schema.avro.cdm18.Subject" in line:
                    # 提取 uuid 和 path
                    subject_uuid = re.findall(
                        'avro.cdm18.Subject":{"uuid":"(.*?)",(.*?)"path":"(.*?)"', line)
                    try:
                        node_uuid = subject_uuid[0][0]
                        node_path = subject_uuid[0][2]
                    except:
                        node_uuid = re.findall(
                            'avro.cdm18.Subject":{"uuid":"(.*?)",(.*?)', line)[0][0]
                        node_path = "null"

                    # 提取 cmdLine
                    subject_cmd = re.findall(
                        ',"cmdLine":{"string":"(.*?)"},', line
                    )
                    node_cmd = subject_cmd[0].split(' ')[0] if len(subject_cmd) > 0 else None

                    # 存储原始数据
                    subject_data[node_uuid] = [node_path, node_cmd]
                    subject_uuids.add(node_uuid)

    # 构建数据库插入数据
    datalist = []
    for uuid in subject_data.keys():
        path, cmd = subject_data[uuid]
        datalist.append([
            uuid,      # 原始 uuid
            path, 
            cmd, 
            index_id
        ])
        index_id += 1

    # 插入数据库
    sql = '''INSERT INTO subject_node_table
             VALUES %s
          '''
    ex.execute_values(cur, sql, datalist, page_size=10000)
    connect.commit()

    return index_id, subject_data  # uuid -> [path, cmdLine]

def store_file(file_path, cur, connect, index_id, filelist):
    file_data = {}  # uuid -> filename
    fail_count = 0
    file_uuids = set()  # 存储所有唯一的 uuid

    for file in tqdm(filelist):
        with open(os.path.join(file_path, file), "r", encoding='utf-8') as f:
            for line in f:
                if "avro.cdm18.FileObject" in line:
                    try:
                        # 提取 uuid 和 filename
                        match = re.findall(
                            'avro.cdm18.FileObject":{"uuid":"(.*?)",(.*?)"filename":"(.*?)"', line)
                        if match:
                            uuid, _, filename = match[0]
                            file_data[uuid] = filename
                            file_uuids.add(uuid)
                    except:
                        fail_count += 1

    print('*' * 10, 'fail_count:', fail_count, '*' * 10)

    # 构建数据库插入数据
    datalist = []
    for uuid in file_data.keys():
        datalist.append([
            uuid,          # 原始 uuid
            file_data[uuid],  # filename
            index_id
        ])
        index_id += 1

    # # 插入数据库
    sql = '''INSERT INTO file_node_table 
             VALUES %s
          '''
    ex.execute_values(cur, sql, datalist, page_size=10000)
    connect.commit()

    return index_id, file_data  # uuid -> filename

def create_node_list(cur):
    nodeid2msg = {}

    # netflow
    sql = """
        select * from netflow_node_table;
        """
    cur.execute(sql)
    records = cur.fetchall()
    for i in records:
        nodeid2msg[i[0]] = i[-1]

    # subject
    sql = """
    select * from subject_node_table;
    """
    cur.execute(sql)
    records = cur.fetchall()
    for i in records:
        nodeid2msg[i[0]] = i[-1]

    # file
    sql = """
    select * from file_node_table;
    """
    cur.execute(sql)
    records = cur.fetchall()
    for i in records:
        nodeid2msg[i[0]] = i[-1]

    return nodeid2msg #{uuid:index_id}

def write_event_in_DB(cur, connect, datalist):
    sql = '''insert into event_table
                         values %s
            '''
    ex.execute_values(cur,sql, datalist,page_size=10000)
    connect.commit()

# def store_event(file_path, cur, connect, nodeid2msg, subject_data, file_data, net_data, filelist, flag):

#     for file in tqdm(filelist):
#         datalist = []
#         with open(os.path.join(file_path, file), "r", encoding='utf-8') as f:
#             for line in f:
#                 if '{"datum":{"com.bbn.tc.schema.avro.cdm18.Event"' in line:
#                     relation_type = re.findall('"type":"(.*?)"', line)[0]
#                     subject_uuid = re.findall('"subject":{"com.bbn.tc.schema.avro.cdm18.UUID":"(.*?)"', line)
#                     predicateObject_uuid = re.findall('"predicateObject":{"com.bbn.tc.schema.avro.cdm18.UUID":"(.*?)"', line)
                    
#                     if len(subject_uuid) > 0 and len(predicateObject_uuid) > 0:
#                         if subject_uuid[0] in subject_data and (predicateObject_uuid[0] in subject_data or
#                                                                         predicateObject_uuid[0] in file_data or
#                                                                         predicateObject_uuid[0] in net_data):
#                             event_uuid = re.findall('{"datum":{"com.bbn.tc.schema.avro.cdm18.Event":{"uuid":"(.*?)",', line)[0]
#                             time_rec = re.findall('"timestampNanos":(.*?),', line)[0]
#                             time_rec = int(time_rec)
#                             subjectId = subject_uuid[0]
#                             objectId = predicateObject_uuid[0]
#                             if objectId in subject_data:
#                                 objectname = subject_data[objectId]
#                                 objtype = "PROCESS"
#                             elif objectId in file_data:
#                                 objectname = file_data[objectId]
#                                 objtype = "FILE"
#                             elif objectId in net_data:
#                                 objectname = net_data[objectId]
#                                 objtype = "NETFLOW"
#                             datalist.append(
#                                 [subjectId, ' '.join(subject_data[subjectId]), nodeid2msg[subjectId], 'PROCESS',
#                                   relation_type, event_uuid, objectId, ' '.join(objectname), nodeid2msg[objectId], objtype, time_rec])
#         sql = '''insert into event_table_6r_{}
#                             values %s
#                 '''.format(flag)
#         ex.execute_values(cur, sql, datalist, page_size=50000)
#         connect.commit()

def store_event(file_path, cur, connect, nodeid2msg, subject_data, file_data, net_data, filelist, flag):
    '''magic 简单图'''
    for file in tqdm(filelist):
        datalist = []
        file_edges = set()
        
        with open(os.path.join(file_path, file), "r", encoding='utf-8') as f:
            for line in f:
                if '{"datum":{"com.bbn.tc.schema.avro.cdm18.Event"' in line:
                    relation_type = re.findall('"type":"(.*?)"', line)[0]
                    subject_uuid = re.findall('"subject":{"com.bbn.tc.schema.avro.cdm18.UUID":"(.*?)"', line)
                    predicateObject_uuid = re.findall('"predicateObject":{"com.bbn.tc.schema.avro.cdm18.UUID":"(.*?)"', line)
                    
                    if len(subject_uuid) > 0 and len(predicateObject_uuid) > 0:
                        if subject_uuid[0] in subject_data and (predicateObject_uuid[0] in subject_data or
                                                                        predicateObject_uuid[0] in file_data or
                                                                        predicateObject_uuid[0] in net_data):
                            #   ============简单图===========
                            edge_key = (subject_uuid[0], predicateObject_uuid[0])
                            if edge_key in file_edges:
                                continue  # 跳过当前文件中已存在的边
                            file_edges.add(edge_key)
                            #   ============简单图===========
                            
                            event_uuid = re.findall('{"datum":{"com.bbn.tc.schema.avro.cdm18.Event":{"uuid":"(.*?)",', line)[0]
                            time_rec = re.findall('"timestampNanos":(.*?),', line)[0]
                            time_rec = int(time_rec)
                            subjectId = subject_uuid[0]
                            objectId = predicateObject_uuid[0]
                            if objectId in subject_data:
                                objectname = subject_data[objectId]
                                objtype = "PROCESS"
                            elif objectId in file_data:
                                objectname = file_data[objectId]
                                objtype = "FILE"
                            elif objectId in net_data:
                                objectname = net_data[objectId]
                                objtype = "NETFLOW"
                            datalist.append(
                                [subjectId, ' '.join(subject_data[subjectId]), nodeid2msg[subjectId], 'PROCESS',
                                  relation_type, event_uuid, objectId, ' '.join(objectname), nodeid2msg[objectId], objtype, time_rec])
        sql = '''insert into event_table_6r_{}
                            values %s
                '''.format(flag)
        ex.execute_values(cur, sql, datalist, page_size=50000)
        connect.commit()

def init_database_connection(database):
    connect = psycopg2.connect(database = database,
                                host = 'localhost',
                                user = 'postgres',
                                password = '123456',
                                port = 5432
                                )
    cur = connect.cursor()
    return cur, connect
    
    
def main():
    database_name = 'tc_theia_dataset_db_magic'
    raw_dir = r"F:\DARPA_TC_E3\data\theia"
    filelist = []
    for file in os.listdir(raw_dir):
        if '-official-6r.' in file:
            filelist.append(file)
    filelist.sort(key=lambda x: int(x.split('.')[-1]) if x.split('.')[-1].isdigit() else 0)
    print(f'待处理：{filelist}')

    cur, connect = init_database_connection(database_name)

    index_id = 0

    print("Processing netflow data")
    index_id, net_data = store_netflow(file_path=raw_dir, cur=cur, connect=connect, index_id=index_id, filelist=filelist)

    print("Processing subject data")
    index_id, subject_data = store_subject(file_path=raw_dir, cur=cur, connect=connect, index_id=index_id, filelist=filelist)

    print("Processing file data")
    index_id, file_data = store_file(file_path=raw_dir, cur=cur, connect=connect, index_id=index_id, filelist=filelist)

    print("Extracting the node list")
    nodeid2msg = create_node_list(cur=cur)  # uuid -> index_id

    print("Processing the events")
    
    for i, file in enumerate(['ta1-theia-e3-official-6r.json', 'ta1-theia-e3-official-6r.json.1', 'ta1-theia-e3-official-6r.json.2', \
        'ta1-theia-e3-official-6r.json.3', 'ta1-theia-e3-official-6r.json.8']):
        name_ = file.split('.')[-1]
        if name_ == 'json':
            name_ = '0'
        store_event(
            file_path=raw_dir,
            cur=cur,
            connect=connect,
            nodeid2msg=nodeid2msg,
            subject_data=subject_data,
            file_data=file_data,
            net_data=net_data,
            filelist=[file],
            flag=name_
        )

if __name__ == '__main__':
    main()