#!/usr/bin/env python3
"""
测试节点形状功能的脚本
展示不同节点类型的可视化效果
"""

import matplotlib.pyplot as plt
import networkx as nx
import numpy as np

def demo_node_shapes():
    """演示不同节点类型的形状"""
    
    print("=== 节点形状演示 ===")
    
    # 定义节点类型和对应的形状
    type_to_shape = {
        0: 'o',  # 圆形 - Process
        1: 's',  # 方形 - File  
        2: '^',  # 三角形 - Socket
        3: 'D',  # 菱形 - Other
        4: 'v',  # 倒三角形
        5: '<',  # 左三角形
        6: '>',  # 右三角形
        7: 'p',  # 五边形
        8: 'h',  # 六边形
        9: '*',  # 星形
    }
    
    type_names = {
        0: 'Process',
        1: 'File', 
        2: 'Socket',
        3: 'Other',
        4: 'Type4',
        5: 'Type5',
        6: 'Type6',
        7: 'Type7',
        8: 'Type8',
        9: 'Type9'
    }
    
    # 创建一个简单的图来展示不同形状
    fig, ax = plt.subplots(1, 1, figsize=(12, 8))
    
    # 创建节点位置
    positions = {}
    node_types = {}
    
    # 排列节点
    rows = 2
    cols = 5
    for i, (node_type, shape) in enumerate(list(type_to_shape.items())[:10]):
        row = i // cols
        col = i % cols
        node_id = i
        positions[node_id] = (col * 2, row * 2)
        node_types[node_id] = node_type
    
    # 按类型绘制节点
    for node_type in range(10):
        # 获取该类型的节点
        type_nodes = [node for node, ntype in node_types.items() if ntype == node_type]
        if not type_nodes:
            continue
            
        shape = type_to_shape[node_type]
        type_name = type_names[node_type]
        
        # 绘制节点
        node_positions = {node: positions[node] for node in type_nodes}
        
        # 为不同类型使用不同颜色
        colors = plt.cm.Set3(node_type / 10.0)
        
        for node in type_nodes:
            x, y = positions[node]
            ax.scatter(x, y, s=800, c=[colors], marker=shape, 
                      edgecolors='black', linewidths=2, alpha=0.8)
            
            # 添加标签
            ax.text(x, y-0.3, f'{type_name}\n(Type {node_type})', 
                   ha='center', va='top', fontsize=10, fontweight='bold')
    
    # 设置图形属性
    ax.set_xlim(-1, 9)
    ax.set_ylim(-1, 3)
    ax.set_aspect('equal')
    ax.grid(True, alpha=0.3)
    ax.set_title('Node Shape Reference\nDifferent shapes for different node types', 
                fontsize=16, pad=20)
    
    # 移除坐标轴
    ax.set_xticks([])
    ax.set_yticks([])
    
    plt.tight_layout()
    plt.savefig('node_shapes_reference.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    print("节点形状参考图已保存为: node_shapes_reference.png")

def print_shape_mapping():
    """打印形状映射表"""
    
    print("\n=== 节点类型与形状映射 ===")
    
    mappings = [
        (0, 'o', 'Process', '圆形'),
        (1, 's', 'File', '方形'),
        (2, '^', 'Socket', '三角形'),
        (3, 'D', 'Other', '菱形'),
        (4, 'v', 'Type4', '倒三角形'),
        (5, '<', 'Type5', '左三角形'),
        (6, '>', 'Type6', '右三角形'),
        (7, 'p', 'Type7', '五边形'),
        (8, 'h', 'Type8', '六边形'),
        (9, '*', 'Type9', '星形'),
    ]
    
    print("类型 | 形状符号 | 名称     | 描述")
    print("-" * 40)
    for type_id, symbol, name, desc in mappings:
        print(f"{type_id:4d} | {symbol:8s} | {name:8s} | {desc}")
    
    print("\n说明:")
    print("- 在可视化图中，每个节点会根据其类型自动选择对应的形状")
    print("- 图例会自动显示图中实际出现的节点类型")
    print("- 中心节点用红色标记，其他节点用蓝色标记")

if __name__ == '__main__':
    demo_node_shapes()
    print_shape_mapping()
