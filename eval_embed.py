import json
import torch
import warnings
import numpy as np
import pickle as pkl
import copy
from utils.loaddata import load_batch_level_dataset, load_entity_level_dataset, load_metadata
from model.autoencoder import build_model
from utils.poolers import Pooling
from utils.utils import set_random_seed
from collections import Counter

from model.eval import batch_level_evaluation, evaluate_entity_level_using_knn
from utils.config import build_args
from plot_utils import *
from darpatc import AddTrigger, TriggerGenerator, get_mal_node_msg, get_map, get_node_map
from attack_utils import add_trigger_to_dataset, deep_clone_dgl_graph, analyze_anomaly_scores
from poison_main import Config
from attack import poison_data, choose_poisoning_node

warnings.filterwarnings('ignore')

test_POISON = True
train_POISON = True

def build_embedding_meta(index_all):
    """
    构建每个嵌入对应的元信息：(图编号, 原始节点编号)
    参数 index_all 是 List[List[int]]
    """
    meta = []
    for i, node_list in enumerate(index_all):
        for nid in node_list:
            meta.append((i, nid))
    return meta

def decode_neighbor_indices(indices, meta):
    """
    将邻接索引映射回原始位置 (图编号, 原始节点编号)
    """
    decoded = []
    for row in indices:
        decoded_row = []
        for idx in row:
            g_id, node_id = meta[idx.item()]
            decoded_row.append((g_id, node_id))
        decoded.append(decoded_row)
    return decoded

def cal_loss_gd(traindata_poidoned_node_hidden_feature, testdata_mal_node_hidden_feature, k=10):
    # 计算测试节点与所有训练投毒节点的距离矩阵 [num_test_mal, num_train_poisoned]
    distances = torch.cdist(testdata_mal_node_hidden_feature, traindata_poidoned_node_hidden_feature, p=2)
    
    # 获取前k个最小距离及其索引
    topk_distances, neighbor_indices = torch.topk(distances, k=k, dim=1, largest=False)
    
    # 计算每个节点的平均k近邻距离
    node_losses = torch.mean(topk_distances, dim=1)
    
    # 总体平均损失
    loss = torch.mean(node_losses)
    
    return loss, neighbor_indices

def cal_loss_g(traindata_poidoned_node_hidden_feature, testdata_mal_node_hidden_feature, k=10, batch_size=1024):
    """
    优化的GPU损失计算函数，支持大tensor的批处理
    """
    device = traindata_poidoned_node_hidden_feature.device
    num_test = testdata_mal_node_hidden_feature.size(0)
    num_train = traindata_poidoned_node_hidden_feature.size(0)

    # 如果数据量不大，直接计算
    if num_test * num_train < 50000000:  # 避免内存溢出
        distances = torch.cdist(testdata_mal_node_hidden_feature, traindata_poidoned_node_hidden_feature, p=2)
        topk_distances, neighbor_indices = torch.topk(distances, k=k, dim=1, largest=False)
        node_losses = torch.mean(topk_distances, dim=1)
        return node_losses, neighbor_indices

    # 对于大tensor，使用批处理
    neighbor_indices = []
    all_node_losses = []
    for i in range(0, num_test, batch_size):
        end_i = min(i + batch_size, num_test)
        test_batch = testdata_mal_node_hidden_feature[i:end_i]

        # 计算当前批次与所有训练数据的距离
        distances = torch.cdist(test_batch, traindata_poidoned_node_hidden_feature, p=2)
        topk_distances, neighbor_indices_batch = torch.topk(distances, k=k, dim=1, largest=False)
        node_losses = torch.mean(topk_distances, dim=1)
        all_node_losses.append(node_losses)
        neighbor_indices.append(neighbor_indices_batch)

    # 合并所有批次的损失
    all_node_losses = torch.cat(all_node_losses, dim=0)
    neighbor_indices = torch.cat(neighbor_indices, dim=0)

    return all_node_losses, neighbor_indices

def main(main_args):
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    dataset_name = main_args.dataset
    with open('./data/{}/node_type_dict.json'.format(dataset_name), 'r', encoding='utf-8') as f:
        node_type_dict = json.load(f)
    node_type_dict_reverse = {v: k for k, v in node_type_dict.items()}  # idx`2`type
    main_args.num_hidden = 64
    main_args.num_layers = 3
    set_random_seed(0)

    if dataset_name == 'streamspot' or dataset_name == 'wget':
        pass
    else:
        metadata = load_metadata(dataset_name)
        cfg = Config()
        cfg.n_dim = metadata['node_feature_dim']
        cfg.e_dim = metadata['edge_feature_dim']
        cfg.node_type_dict, cfg.edge_type_dict = get_map(cfg.dataset)
        
        train_data_all = []
        test_data_all = []
        for i in range(metadata['n_train']):
            g = load_entity_level_dataset(cfg.dataset, 'train', i).to(device)
            train_data_all.append(g)
        for i in range(metadata['n_test']):
            g = load_entity_level_dataset(cfg.dataset, 'test', i).to(device)
            test_data_all.append(g)
               
        main_args.n_dim = metadata['node_feature_dim']
        main_args.e_dim = metadata['edge_feature_dim']
        model = build_model(main_args)
        model.load_state_dict(torch.load("./checkpoints/checkpoint-{}.pt".format(dataset_name), map_location=device))
        model = model.to(device)
        model.eval()
        malicious, _ = metadata['malicious']
        n_train = metadata['n_train']
        n_test = metadata['n_test']
        train_node_map, test_node_map = get_node_map(cfg.dataset)
        train_node_map = [{v: k for k, v in train_node_map1.items()} for train_node_map1 in train_node_map]
        test_node_map = {v: k for k, v in test_node_map.items()}
        
        malicious_node, mal_file_msg, mal_socket_msg = get_mal_node_msg(cfg, test_data_all[0])
        candidates_all = choose_poisoning_node(cfg, train_data_all, None, None, sim=True)
        
        # 投毒节点索引
        poisoned_process_idx = candidates_all['SUBJECT_PROCESS']
        poisoned_socket_idx = []
        poisoned_file_idx = []
        for candidates_graph in candidates_all['NetFlowObject']:
            poisoned_socket_idx_g = []
            for _, value in candidates_graph.items():
                poisoned_socket_idx_g.extend(value)
            poisoned_socket_idx.append(list(set(poisoned_socket_idx_g)))
        for candidates_graph in candidates_all['FILE_OBJECT_BLOCK']:
            poisoned_file_idx_g = []
            for _, value in candidates_graph.items():
                poisoned_file_idx_g.extend(value)
            poisoned_file_idx.append(list(set(poisoned_file_idx_g)))
        print(f'各类型投毒节点数目：进程：{sum(len(sub) for sub in poisoned_process_idx)}  文件：{sum(len(sub) for sub in poisoned_file_idx)}  socket：{sum(len(sub) for sub in poisoned_socket_idx)}')
        
        # for i in range(n_train):
        #     g = train_data_all[i]
        #     p_idx = poisoned_process_idx[i]
        #     # p_idx = (g.ndata['type'] == cfg.node_type_dict['SUBJECT_PROCESS']).nonzero().squeeze().tolist()
        #     for idx in p_idx:
        #         c = g.in_edges(idx, 'eid')
        #         type_ = g.edata['type'][c]
        #         if len(type_)<=5 and 1 in type_ and 4 in type_  and 8 not in type_:
        #             print(i, idx, type_)
        # input('ddddddddd')
        
        test_data_all_clean = [deep_clone_dgl_graph(test_data_all_i) for test_data_all_i in test_data_all]
        if train_POISON:
            print('训练集投毒...')
            # 添加触发器
            trigger_generator = TriggerGenerator(cfg.n_dim, cfg.e_dim, cfg.trigger_shape)
            # trigger_generator.load_state_dict(torch.load('./poison_model/trigger_generator50.pth'))
            trigger_generator.eval().to(device)
            addtrigger = AddTrigger(cfg)
            for i in range(n_train):
                g = train_data_all[i]
                poisoned_g = poison_data(cfg, trigger_generator, g, candidates_all['SUBJECT_PROCESS'][i], candidates_all['NetFlowObject'][i], candidates_all['FILE_OBJECT_BLOCK'][i], train=True)
                train_data_all[i] = poisoned_g
                del g
                
        if test_POISON:
            print('测试集投毒...')
            for i in range(n_test):
                g = test_data_all[i]
                poisoned_g = poison_data(cfg, trigger_generator, g, malicious_node['SUBJECT_PROCESS'], mal_socket_msg, mal_file_msg, train=False)
                test_data_all[i] = poisoned_g
                del g
            
        with torch.no_grad():
            x_train_all = []
            for i in range(n_train):
                x_train_all.append(model.embed(train_data_all[i]))
            
            x_test_all = []
            for i in range(n_test):
                x_test_all.append(model.embed(test_data_all[i]))
            
            x_test_clean_all = []
            for i in range(n_test):
                x_test_clean_all.append(model.embed(test_data_all_clean[i]))
            
            x_train_poisoned_idx_all = {
                'SUBJECT_PROCESS':copy.deepcopy(poisoned_process_idx),
                'FILE_OBJECT_BLOCK':copy.deepcopy(poisoned_file_idx),
                'NetFlowObject':copy.deepcopy(poisoned_socket_idx)
                }  # 中毒节点的索引
            
        for viz_node_type in ['SUBJECT_PROCESS', 'FILE_OBJECT_BLOCK', 'NetFlowObject']:  #
            # 干净恶意节点
            x_test_m_clean = x_test_clean_all[0][malicious_node[viz_node_type]]
            
            
            # test  恶意节点  和   同类型良性
            x_test_m = x_test_all[0][malicious_node[viz_node_type]]
            mal_mask = torch.ones(test_data_all[0].ndata['type'].shape[0]).to(device)
            mal_mask[malicious_node[viz_node_type]] = 0
            all_type_nodes_idx = ((test_data_all[0].ndata['type']==cfg.node_type_dict[viz_node_type]) & mal_mask.bool()).nonzero().squeeze()
            x_test_b = x_test_all[0][all_type_nodes_idx]  # 同类型良性

            # train
            x_train_clean = []               # 所有干净节点嵌入
            x_train_clean_meta = []         # 对应的原始索引 (图编号, 节点编号)
            x_train_poisoned = []           # 所有投毒节点嵌入
            x_train_poisoned_meta = []      # 对应的原始索引

            for i in range(n_train):
                poison_mask = torch.ones(train_data_all[i].ndata['type'].shape[0]).to(device)
                poison_mask[x_train_poisoned_idx_all[viz_node_type][i]] = 0
                all_type_nodes_idx = ((train_data_all[i].ndata['type']==cfg.node_type_dict[viz_node_type]) & poison_mask.bool()).nonzero().squeeze()
                
                # 干净节点
                x_train_clean.append(x_train_all[i][all_type_nodes_idx])
                for nid in all_type_nodes_idx.tolist():
                    x_train_clean_meta.append((i, nid))
                
                # 投毒节点
                x_train_poisoned.append(x_train_all[i][x_train_poisoned_idx_all[viz_node_type][i]])
                for nid in x_train_poisoned_idx_all[viz_node_type][i]:
                    x_train_poisoned_meta.append((i, nid))

            x_train_clean = torch.cat(x_train_clean, dim=0)
            x_train_poisoned = torch.cat(x_train_poisoned, dim=0)

            # 计算嵌入空间中的距离
            loss1, neighbor_indices1 = cal_loss_g(x_train_poisoned, x_test_m, k=10)  # poisoned
            loss2, neighbor_indices2 = cal_loss_g(x_train_clean, x_test_m, k=10)  # clean
            
            loss3, neighbor_indices3 = cal_loss_g(x_train_poisoned, x_test_m_clean, k=10)  # clean test
            loss4, neighbor_indices4 = cal_loss_g(x_train_clean, x_test_m_clean, k=10)  # clean test

            # 邻居还原
            decoded_poisoned_neighbors = decode_neighbor_indices(neighbor_indices1, x_train_poisoned_meta)
            decoded_clean_neighbors = decode_neighbor_indices(neighbor_indices2, x_train_clean_meta)
            a = 1

            # 可选：保存邻居映射
            # with open(f'./match/{viz_node_type}_mal_to_poisoned.json', 'w') as f:
            #     json.dump(decoded_poisoned_neighbors, f)
            # with open(f'./match/{viz_node_type}_mal_to_clean.json', 'w') as f:
            #     json.dump(decoded_clean_neighbors, f)
        
            
            
            
            # for viz_node_type in ['SUBJECT_PROCESS', 'FILE_OBJECT_BLOCK', 'NetFlowObject']:  #
            #     # test  仅恶意  和   同类型良性
            #     x_test_m = x_test_all[0][malicious_node[viz_node_type]]
            #     mal_mask = torch.ones(test_data_all[0].ndata['type'].shape[0]).to(device)
            #     mal_mask[malicious_node[viz_node_type]] = 0
            #     all_type_nodes_idx = ((test_data_all[0].ndata['type']==cfg.node_type_dict[viz_node_type])&mal_mask.bool()).nonzero().squeeze()
            #     x_test_b = x_test_all[0][all_type_nodes_idx]  # 同类型良性
                
            #     # train
            #     x_train = []  # 该类型未投毒所有节点
            #     x_train_poisoned = []  # 该类型投毒节点
            #     x_train_poisoned_idx = x_train_poisoned_idx_all[viz_node_type]  # 该类型投毒节点索引，多个图
            #     train_num = []
            #     for i in range(n_train):
            #         poison_mask = torch.ones(train_data_all[i].ndata['type'].shape[0]).to(device)
            #         poison_mask[x_train_poisoned_idx[i]] = 0
            #         all_type_nodes_idx = ((train_data_all[i].ndata['type']==cfg.node_type_dict[viz_node_type])&poison_mask.bool()).nonzero().squeeze()
            #         x_train.append(x_train_all[i][all_type_nodes_idx])
            #         x_train_poisoned.append(x_train_all[i][x_train_poisoned_idx[i]])
            #         train_num.append(len(x_train_poisoned_idx[i]))
            #         # globalize_num.append()
            #     x_train = torch.cat(x_train, dim=0)
                
            #     x_train_poisoned = torch.cat(x_train_poisoned, dim=0)
            #     loss1, neighbor_indices1 = cal_loss_g(x_train_poisoned, x_test_m, k=10)  # 投毒节点距离
            #     loss2, neighbor_indices2 = cal_loss_g(x_train, x_test_m, k=10)  # 干净节点距离
            #     print('mal',viz_node_type, loss1, loss2)
                # loss1, _ = cal_loss_g(x_train_poisoned, x_test_b, k=10)
                # loss2, _ = cal_loss_g(x_train, x_test_b, k=10)
                # print('ben', viz_node_type, loss1, loss2)
                
                # cumulative_num = torch.cumsum(torch.tensor(train_num), dim=0)
                # original_positions = {}
                # for neighbor_idx in neighbor_indices1.unique():
                #     graph_idx = torch.where(neighbor_idx < cumulative_num)[0][0].item()
                #     local_idx = neighbor_idx - cumulative_num[graph_idx]
                #     original_idx = x_train_poisoned_idx[graph_idx][local_idx]
                #     if graph_idx not in original_positions:
                #         original_positions[graph_idx] = []
                #     original_positions[graph_idx].append(original_idx)
                # a = 1
                # with open('./match/process_idx.pkl', 'wb') as f:
                #     pkl.dump(original_positions, f)
     
    return


if __name__ == '__main__':
    args = build_args()
    main(args)
